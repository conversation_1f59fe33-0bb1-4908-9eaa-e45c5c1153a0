<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Edit Rating Item</h5>
        <a href="<?= base_url('dakoii/rating_items/list') ?>" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
    <div class="card-body">
        <form action="<?= base_url('dakoii/rating_items/update/' . $item['id']) ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="mb-3">
                <label for="item_label" class="form-label">Item Label</label>
                <input type="text" class="form-control" id="item_label" name="item_label" value="<?= old('item_label', $item['item_label']) ?>" required>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['item_label'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['item_label'] ?></div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Enter a description for this rating item (optional)"><?= old('description', $item['description'] ?? '') ?></textarea>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['description'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['description'] ?></div>
                <?php endif; ?>
                <div class="form-text">Provide a description of what this rating item represents.</div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Item
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Rating Scores for this Item -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Rating Scores</h5>
        <div>
            <a href="<?= base_url('dakoii/rating_scores/import/' . $item['id']) ?>" class="btn btn-success btn-sm me-2">
                <i class="fas fa-upload"></i> Import CSV
            </a>
            <a href="<?= base_url('dakoii/rating_scores/new/' . $item['id']) ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-plus-circle"></i> Add New Score
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($scores)) : ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No rating scores found for this item.
            </div>
        <?php else : ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">Score</th>
                            <th width="20%">Label</th>
                            <th>Description</th>
                            <th width="15%">Created At</th>
                            <th width="15%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($scores as $index => $score) : ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($score['score']) ?></span>
                                </td>
                                <td>
                                    <strong class="text-primary"><?= esc($score['label']) ?></strong>
                                </td>
                                <td>
                                    <?php if (!empty($score['score_description'])) : ?>
                                        <span class="text-muted"><?= esc($score['score_description']) ?></span>
                                    <?php else : ?>
                                        <em class="text-muted">No description</em>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($score['created_at'])) ?></td>
                                <td>
                                    <a href="<?= base_url('dakoii/rating_scores/edit/' . $score['id']) ?>" class="btn btn-info btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?= base_url('dakoii/rating_scores/delete/' . $score['id']) ?>" class="btn btn-danger btn-sm" title="Delete" onclick="return confirm('Are you sure you want to delete this score?')">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?> 