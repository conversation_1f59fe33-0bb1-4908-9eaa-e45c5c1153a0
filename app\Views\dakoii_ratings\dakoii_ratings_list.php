<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Rating Items</h5>
        <div>
            <a href="<?= base_url('dakoii/rating_items/import') ?>" class="btn btn-success btn-sm me-2">
                <i class="fas fa-upload"></i> Import CSV
            </a>
            <a href="<?= base_url('dakoii/rating_items/new') ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-plus-circle"></i> Add New Item
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($items)) : ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No rating items found.
            </div>
        <?php else : ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th>Item Label</th>
                            <th>Description</th>
                            <th width="15%">Scoring Items</th>
                            <th width="15%">Created At</th>
                            <th width="15%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $index => $item) : ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><?= esc($item['item_label']) ?></td>
                                <td>
                                    <?php if (!empty($item['description'])) : ?>
                                        <span class="text-muted"><?= esc($item['description']) ?></span>
                                    <?php else : ?>
                                        <em class="text-muted">No description</em>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= $item['score_count'] ?> <?= $item['score_count'] == 1 ? 'item' : 'items' ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($item['created_at'])) ?></td>
                                <td>
                                    <a href="<?= base_url('dakoii/rating_items/edit/' . $item['id']) ?>" class="btn btn-info btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?= base_url('dakoii/rating_items/delete/' . $item['id']) ?>" class="btn btn-danger btn-sm" title="Delete" onclick="return confirm('Are you sure you want to delete this item? This will also delete all associated scores.')">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?> 