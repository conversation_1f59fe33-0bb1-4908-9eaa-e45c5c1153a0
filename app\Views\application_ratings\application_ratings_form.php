<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Rate Applicant: <?= esc($application['first_name'] . ' ' . $application['last_name']) ?></h1>
            <div class="d-flex gap-2 mt-2">
                <span class="badge bg-primary">View Profile</span>
                <span class="badge bg-secondary">Ratings</span>
                <span class="badge bg-info">View Applicant</span>
                <span class="badge bg-success">Rate Applicant</span>
            </div>
        </div>
        <div>
            <button class="btn btn-primary">
                <i class="fas fa-star me-2"></i>Rate Applicant
            </button>
        </div>
    </div>

    <!-- Two Column Layout -->
    <div class="row">
        <!-- Left Column - Applicant Profile -->
        <div class="col-lg-6">
            <div class="card mb-4" id="applicantSpecificationsCard">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Applicant Specifications
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($profile): ?>
                        <!-- Basic Information -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Basic Information</h6>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Full Name:</strong><br>
                                    <?= esc($profile['full_name']) ?>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Sex:</strong><br>
                                    <?= esc($profile['sex']) ?>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-sm-6">
                                    <strong>Age:</strong><br>
                                    <?= esc($profile['bdate_age']) ?>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Place of Origin:</strong><br>
                                    <?= esc($profile['place_origin']) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Details -->
                        <?php if (!empty($profile['contact_details'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Contact Details</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['contact_details'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Current Employment -->
                        <?php if (!empty($profile['current_employer']) || !empty($profile['current_position'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Current Employment</h6>
                            <?php if (!empty($profile['current_employer'])): ?>
                                <strong>Current Employer:</strong><br>
                                <?= esc($profile['current_employer']) ?><br>
                            <?php endif; ?>
                            <?php if (!empty($profile['current_position'])): ?>
                                <strong>Current Position:</strong><br>
                                <?= esc($profile['current_position']) ?>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Address -->
                        <?php if (!empty($profile['address_location'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Address/Location</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['address_location'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- ID Document Numbers -->
                        <?php if (!empty($profile['id_document_numbers'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">ID Numbers</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['id_document_numbers'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Qualifications -->
                        <?php if (!empty($profile['qualification_text'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Qualifications</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto;">
                                <?= nl2br(esc($profile['qualification_text'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Job Experiences -->
                        <?php if (!empty($profile['job_experiences'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Job Experiences</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto;">
                                <?= nl2br(esc($profile['job_experiences'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Skills and Competencies -->
                        <?php if (!empty($profile['skills_competencies'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Skills and Competencies</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto;">
                                <?= nl2br(esc($profile['skills_competencies'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Knowledge -->
                        <?php if (!empty($profile['knowledge'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Knowledge</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto;">
                                <?= nl2br(esc($profile['knowledge'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Other Trainings -->
                        <?php if (!empty($profile['other_trainings'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Trainings</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto;">
                                <?= nl2br(esc($profile['other_trainings'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Publications -->
                        <?php if (!empty($profile['publications'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Publications</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['publications'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Awards -->
                        <?php if (!empty($profile['awards'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Awards</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['awards'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Referees -->
                        <?php if (!empty($profile['referees'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Referees</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['referees'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Comments -->
                        <?php if (!empty($profile['comments'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">Comments</h6>
                            <div class="text-break">
                                <?= nl2br(esc($profile['comments'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No profile information available for this applicant in this exercise.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Column - Position Specifications & Rating Form -->
        <div class="col-lg-6">
            <!-- Position Specifications -->
            <div class="card mb-4" id="positionSpecificationsCard">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>Position Specifications
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($position): ?>
                        <!-- Position Basic Info -->
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2"><?= esc($position['designation']) ?></h6>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Position No:</strong><br>
                                    <?= esc($position['position_reference']) ?>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Classification:</strong><br>
                                    <?= esc($position['classification']) ?>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-sm-6">
                                    <strong>Location:</strong><br>
                                    <?= esc($position['location']) ?>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Annual Salary:</strong><br>
                                    <?= esc($position['annual_salary']) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Exercise Information -->
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Exercise Information</h6>
                            <strong>Exercise:</strong> <?= esc($position['exercise_name']) ?><br>
                            <strong>Advertisement No:</strong> <?= esc($position['advertisement_no']) ?><br>
                            <strong>Organization:</strong> <?= esc($position['org_name']) ?>
                        </div>

                        <!-- Qualifications Required -->
                        <?php if (!empty($position['qualifications'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Qualifications</h6>
                            <div class="text-break" style="max-height: 150px; overflow-y: auto;">
                                <?= nl2br(esc($position['qualifications'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Knowledge Required -->
                        <?php if (!empty($position['knowledge'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Knowledge</h6>
                            <div class="text-break" style="max-height: 150px; overflow-y: auto;">
                                <?= nl2br(esc($position['knowledge'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Skills & Competencies Required -->
                        <?php if (!empty($position['skills_competencies'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Skills and Competencies</h6>
                            <div class="text-break" style="max-height: 150px; overflow-y: auto;">
                                <?= nl2br(esc($position['skills_competencies'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Job Experiences Required -->
                        <?php if (!empty($position['job_experiences'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Job Experiences</h6>
                            <div class="text-break" style="max-height: 150px; overflow-y: auto;">
                                <?= nl2br(esc($position['job_experiences'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Job Description File -->
                        <?php if (!empty($position['jd_filepath'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Job Description Document</h6>
                            <div class="alert alert-light border">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="fas fa-file-pdf text-danger fa-2x"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Complete Job Description</h6>
                                        <p class="mb-2 small">Full position requirements and specifications</p>
                                        <div class="d-flex gap-2">
                                            <a href="<?= base_url($position['jd_filepath']) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-download me-1"></i> Download JD
                                            </a>
                                            <button type="button" class="btn btn-sm btn-primary" onclick="toggleJDViewer()">
                                                <i class="fas fa-eye me-1"></i> View JD
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- PDF Viewer Container -->
                                <div id="jdViewerContainer" style="display: none;" class="mt-3">
                                    <div class="border rounded p-3 bg-white">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0 text-primary">
                                                <i class="fas fa-file-pdf me-2"></i>Job Description PDF Viewer
                                            </h6>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()" title="Zoom Out">
                                                    <i class="fas fa-search-minus"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()" title="Zoom In">
                                                    <i class="fas fa-search-plus"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="toggleJDViewer()" title="Close Viewer">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Loading indicator -->
                                        <div id="pdfLoadingIndicator" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading PDF...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Loading PDF document...</p>
                                        </div>

                                        <!-- PDF Canvas -->
                                        <div id="pdfCanvasContainer" style="display: none;">
                                            <canvas id="jdPdfCanvas" class="border w-100 d-block mx-auto" style="max-height: 600px;"></canvas>
                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-secondary" onclick="prevPage()" id="prevPageBtn">
                                                        <i class="fas fa-chevron-left"></i> Previous
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="nextPage()" id="nextPageBtn">
                                                        Next <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </div>
                                                <div class="text-center">
                                                    <small class="text-muted">
                                                        Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted">
                                                        Zoom: <span id="currentZoom">100</span>%
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Extracted JD Text -->
                        <?php if (!empty($position['jd_texts_extracted'])): ?>
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2">Job Description Details</h6>
                            <div class="text-break" style="max-height: 200px; overflow-y: auto; font-size: 0.9em;">
                                <?= nl2br(esc($position['jd_texts_extracted'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Position information not available.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row - AI Assistant and Rating Form -->
    <div class="row mt-4">
        <!-- Left Column - AI Rating Assistant -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>AI Rating Assistant
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="spinner-border text-primary mb-3" role="status" style="display: none;" id="aiLoadingSpinner">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <button type="button" class="btn btn-primary btn-lg" id="analyzeApplicantBtn">
                            <i class="fas fa-magic me-2"></i>Analyze Applicant
                        </button>
                    </div>

                    <div id="aiAnalysisResult" style="display: none;">
                        <div class="alert alert-light border">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-brain me-2"></i>AI Analysis Results
                            </h6>
                            <div id="aiAnalysisContent">
                                <!-- AI analysis will be populated here -->
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-primary">Suggested Ratings:</h6>
                            <div id="aiSuggestedRatings" class="row">
                                <!-- Suggested ratings will be populated here -->
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="applyAiRatingsBtn">
                                <i class="fas fa-check me-2"></i>Apply AI Suggestions
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="clearAiAnalysisBtn">
                                <i class="fas fa-times me-2"></i>Clear Analysis
                            </button>
                        </div>
                    </div>

                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            The AI assistant analyzes the applicant's profile against position requirements to provide rating suggestions. Final ratings are at your discretion.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Rating Form -->
        <div class="col-lg-6">
            <div class="card" id="ratingFormCard">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Rate Applicant
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($existingRatings) && $application['rating_status'] === 'completed'): ?>
                        <!-- Show Existing Rating -->
                        <div id="existingRatingView">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                This application has already been rated. <strong>Total Score:</strong> <?= $application['rating_capability_max'] ?>
                            </div>
                            <div class="mb-3">
                                <strong>Rating Remarks:</strong><br>
                                <?= nl2br(esc($application['rating_remarks'])) ?>
                            </div>
                            <div class="text-muted mb-3">
                                Rated by: <?= $application['rated_by'] ?> on <?= date('M d, Y H:i', strtotime($application['rated_at'])) ?>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="<?= base_url('rating/applications/' . $application['position_id']) ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Applications
                                </a>
                                <button type="button" class="btn btn-primary" onclick="toggleEditRating()">
                                    <i class="fas fa-edit me-2"></i>Edit Rating
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Rating Form (Always present, hidden when showing existing ratings) -->
                    <form method="POST" action="<?= base_url('rating/submit') ?>" id="ratingForm" <?= (!empty($existingRatings) && $application['rating_status'] === 'completed') ? 'style="display: none;"' : '' ?>>
                        <?= csrf_field() ?>
                        <input type="hidden" name="application_id" value="<?= $application['id'] ?>">

                        <h6 class="mb-3">Rate the following criteria:</h6>
                        <!-- Rating Form -->
                        <form method="POST" action="<?= base_url('rating/submit') ?>" id="ratingForm">
                            <?= csrf_field() ?>
                            <input type="hidden" name="application_id" value="<?= $application['id'] ?>">

                            <h6 class="mb-3">Rate the following criteria:</h6>

                            <?php if (!empty($ratingItems)): ?>
                                <?php foreach ($ratingItems as $ratingItem): ?>
                                    <?php $item = $ratingItem['item']; ?>
                                    <?php $scores = $ratingItem['scores']; ?>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            <?= esc($item['item_label']) ?>
                                            <?php if (!empty($scores)): ?>
                                                (Max: <?= max(array_column($scores, 'score')) ?>)
                                            <?php endif; ?>
                                        </label>

                                        <?php if (!empty($item['description'])): ?>
                                            <div class="text-muted small mb-2"><?= esc($item['description']) ?></div>
                                        <?php endif; ?>

                                        <?php
                                        // Get existing rating value for this item
                                        $existingValue = '';
                                        if (!empty($existingRatings)) {
                                            foreach ($existingRatings as $rating) {
                                                if ($rating['rate_item_id'] == $item['id']) {
                                                    $existingValue = $rating['score_set']; // Use score_set field
                                                    break;
                                                }
                                            }
                                        }
                                        ?>
                                        <select name="ratings[<?= $item['id'] ?>]" class="form-select rating-select" required data-max-score="<?= !empty($scores) ? max(array_column($scores, 'score')) : 0 ?>">
                                            <option value="">Select Rating</option>
                                            <?php if (!empty($scores)): ?>
                                                <?php foreach ($scores as $score): ?>
                                                    <option value="<?= $score['score'] ?>" <?= ($existingValue == $score['score']) ? 'selected' : '' ?>>
                                                        <?= $score['score'] ?> - <?= esc($score['label']) ?>
                                                        <?php if (!empty($score['score_description'])): ?>
                                                            (<?= esc($score['score_description']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <option value="0">No scores available</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No rating criteria have been configured. Please contact the administrator.
                                </div>
                            <?php endif; ?>

                            <!-- Rating Remarks -->
                            <div class="mb-3">
                                <label class="form-label">Rating Remarks</label>
                                <textarea name="remarks" class="form-control" rows="4" placeholder="Enter your comments about this applicant..."><?= !empty($application['rating_remarks']) ? esc($application['rating_remarks']) : '' ?></textarea>
                            </div>

                            <!-- Total Score Display -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                                    <strong>Total Score: <span id="totalScore">0</span>/<span id="maxScore"><?= array_sum(array_map(function($item) { return !empty($item['scores']) ? max(array_column($item['scores'], 'score')) : 0; }, $ratingItems ?? [])) ?></span></strong>
                                    <div class="progress flex-grow-1 ms-3" style="height: 25px;">
                                        <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i><?= (!empty($existingRatings) && $application['rating_status'] === 'completed') ? 'Update Rating' : 'Submit Rating' ?>
                                </button>
                                <?php if (!empty($existingRatings) && $application['rating_status'] === 'completed'): ?>
                                    <button type="button" class="btn btn-secondary" onclick="toggleEditRating()">
                                        <i class="fas fa-times me-2"></i>Cancel Edit
                                    </button>
                                <?php else: ?>
                                    <a href="<?= base_url('rating/applications/' . $application['position_id']) ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Rating Summary Section -->
    <?php if (!empty($existingRatings)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Rating Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Criteria</th>
                                    <th>Rating</th>
                                    <th>Out of</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Age</td>
                                    <td>7</td>
                                    <td>8</td>
                                </tr>
                                <tr>
                                    <td>Qualification</td>
                                    <td>8</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>Experience</td>
                                    <td>7</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>Training</td>
                                    <td>6</td>
                                    <td>5</td>
                                </tr>
                                <tr>
                                    <td>Skills and Competencies</td>
                                    <td>6</td>
                                    <td>5</td>
                                </tr>
                                <tr>
                                    <td>Knowledge</td>
                                    <td>1</td>
                                    <td>1</td>
                                </tr>
                                <tr>
                                    <td>Capability</td>
                                    <td>2</td>
                                    <td>3</td>
                                </tr>
                                <tr>
                                    <td>Public Service Status</td>
                                    <td>1</td>
                                    <td>1</td>
                                </tr>
                                <tr class="table-dark">
                                    <td><strong>Total</strong></td>
                                    <td><strong>38</strong></td>
                                    <td><strong>43</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<script>
// PDF.js Configuration
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// PDF Viewer Variables
let pdfDoc = null;
let pageNum = 1;
let pageRendering = false;
let pageNumPending = null;
let scale = 1.0;
let canvas = null;
let ctx = null;

// PDF Viewer Functions
function toggleJDViewer() {
    const container = document.getElementById('jdViewerContainer');
    if (container.style.display === 'none') {
        container.style.display = 'block';
        if (!pdfDoc) {
            loadPDF();
        }
    } else {
        container.style.display = 'none';
    }
}

function loadPDF() {
    const pdfUrl = '<?= base_url($position['jd_filepath'] ?? '') ?>';
    if (!pdfUrl) {
        alert('PDF file not available');
        return;
    }

    const loadingIndicator = document.getElementById('pdfLoadingIndicator');
    const canvasContainer = document.getElementById('pdfCanvasContainer');

    loadingIndicator.style.display = 'block';
    canvasContainer.style.display = 'none';

    canvas = document.getElementById('jdPdfCanvas');
    ctx = canvas.getContext('2d');

    pdfjsLib.getDocument(pdfUrl).promise.then(function(pdfDoc_) {
        pdfDoc = pdfDoc_;
        document.getElementById('totalPages').textContent = pdfDoc.numPages;

        loadingIndicator.style.display = 'none';
        canvasContainer.style.display = 'block';

        // Initial page render
        renderPage(pageNum);
    }).catch(function(error) {
        console.error('Error loading PDF:', error);
        loadingIndicator.innerHTML = '<div class="alert alert-danger">Error loading PDF. Please try downloading the file instead.</div>';
    });
}

function renderPage(num) {
    pageRendering = true;

    pdfDoc.getPage(num).then(function(page) {
        const viewport = page.getViewport({scale: scale});
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };

        const renderTask = page.render(renderContext);

        renderTask.promise.then(function() {
            pageRendering = false;
            if (pageNumPending !== null) {
                renderPage(pageNumPending);
                pageNumPending = null;
            }
        });
    });

    document.getElementById('currentPage').textContent = num;
    updateNavigationButtons();
}

function queueRenderPage(num) {
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}

function prevPage() {
    if (pageNum <= 1) {
        return;
    }
    pageNum--;
    queueRenderPage(pageNum);
}

function nextPage() {
    if (pageNum >= pdfDoc.numPages) {
        return;
    }
    pageNum++;
    queueRenderPage(pageNum);
}

function zoomIn() {
    scale += 0.2;
    document.getElementById('currentZoom').textContent = Math.round(scale * 100);
    queueRenderPage(pageNum);
}

function zoomOut() {
    if (scale <= 0.4) {
        return;
    }
    scale -= 0.2;
    document.getElementById('currentZoom').textContent = Math.round(scale * 100);
    queueRenderPage(pageNum);
}

function updateNavigationButtons() {
    document.getElementById('prevPageBtn').disabled = (pageNum <= 1);
    document.getElementById('nextPageBtn').disabled = (pageNum >= pdfDoc.numPages);
}

// Calculate total score dynamically
function calculateTotalScore() {
    let total = 0;
    let maxTotal = 0;
    const selects = document.querySelectorAll('.rating-select');

    selects.forEach(function(select) {
        const value = parseInt(select.value) || 0;
        const maxScore = parseInt(select.getAttribute('data-max-score')) || 0;
        total += value;
        maxTotal += maxScore;
    });

    document.getElementById('totalScore').textContent = total;
    document.getElementById('maxScore').textContent = maxTotal;

    // Update progress bar
    const percentage = maxTotal > 0 ? (total / maxTotal) * 100 : 0;
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';

    // Color coding for progress bar
    if (percentage < 50) {
        progressBar.className = 'progress-bar bg-danger';
    } else if (percentage < 75) {
        progressBar.className = 'progress-bar bg-warning';
    } else {
        progressBar.className = 'progress-bar bg-success';
    }
}

// Add event listeners to rating selects
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('.rating-select');
    selects.forEach(function(select) {
        select.addEventListener('change', calculateTotalScore);
    });

    // AI Assistant event listeners
    const analyzeBtn = document.getElementById('analyzeApplicantBtn');
    const applyBtn = document.getElementById('applyAiRatingsBtn');
    const clearBtn = document.getElementById('clearAiAnalysisBtn');

    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeApplicant);
    }

    if (applyBtn) {
        applyBtn.addEventListener('click', applyAiRatings);
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', clearAiAnalysis);
    }
});

// Toggle edit rating form
function toggleEditRating() {
    const form = document.getElementById('ratingForm');
    const existingView = document.getElementById('existingRatingView');

    if (form && existingView) {
        // Toggle between showing existing rating and edit form
        if (form.style.display === 'none') {
            form.style.display = 'block';
            existingView.style.display = 'none';
        } else {
            form.style.display = 'none';
            existingView.style.display = 'block';
        }

        // Recalculate total score when showing the form
        if (form.style.display === 'block') {
            calculateTotalScore();
        }
    }
}

// Extract structured page content for AI analysis
function extractPageContent() {
    let structuredContent = {
        applicant_info: '',
        position_details: '',
        jd_file_info: '',
        rating_criteria: '',
        raw_content: ''
    };

    // Extract Applicant Information using ID
    const applicantCard = document.getElementById('applicantSpecificationsCard');
    if (applicantCard) {
        const applicantBody = applicantCard.querySelector('.card-body');
        if (applicantBody) {
            structuredContent.applicant_info = applicantBody.innerText.trim();
        }
    }

    // Extract Position Specifications using ID
    const positionCard = document.getElementById('positionSpecificationsCard');
    if (positionCard) {
        const positionBody = positionCard.querySelector('.card-body');
        if (positionBody) {
            structuredContent.position_details = positionBody.innerText.trim();
        }
    }

    // Extract JD file information if available
    <?php if (!empty($position['jd_filepath'])): ?>
    structuredContent.jd_file_info = 'JOB DESCRIPTION FILE AVAILABLE:\n';
    structuredContent.jd_file_info += 'File Path: <?= esc($position['jd_filepath']) ?>\n';
    structuredContent.jd_file_info += 'Note: Complete job description document is available for detailed position requirements.\n';
    structuredContent.jd_file_info += 'This document contains comprehensive information about the position specifications, requirements, and expectations.\n';
    <?php endif; ?>

    // Extract Rating Criteria and Scoring Options using ID
    const ratingFormCard = document.getElementById('ratingFormCard');
    if (ratingFormCard) {
        const ratingForm = ratingFormCard.querySelector('form[action*="submit"]');
        if (ratingForm) {
            let criteriaText = 'RATING CRITERIA AND SCORING OPTIONS:\n\n';

            const ratingSelects = ratingForm.querySelectorAll('.rating-select');
        ratingSelects.forEach((select, index) => {
            const label = select.closest('.mb-3').querySelector('label');
            const description = select.closest('.mb-3').querySelector('.text-muted');

            if (label) {
                criteriaText += `${index + 1}. ${label.innerText.trim()}\n`;

                if (description) {
                    criteriaText += `   Description: ${description.innerText.trim()}\n`;
                }

                criteriaText += '   Scoring Options:\n';
                const options = select.querySelectorAll('option');
                options.forEach(option => {
                    if (option.value && option.value !== '') {
                        criteriaText += `   - ${option.innerText.trim()}\n`;
                    }
                });
                criteriaText += '\n';
            }
        });

            structuredContent.rating_criteria = criteriaText;
        }
    }

    // Fallback: Get all card content as before
    const allCards = document.querySelectorAll('.card .card-body');
    let rawContent = '';
    allCards.forEach((card, index) => {
        const cardText = card.innerText.trim();
        if (cardText) {
            rawContent += `SECTION ${index + 1}:\n${cardText}\n\n`;
        }
    });
    structuredContent.raw_content = rawContent;

    // Build final content string
    let finalContent = '';

    if (structuredContent.applicant_info) {
        finalContent += '=== APPLICANT INFORMATION ===\n';
        finalContent += structuredContent.applicant_info + '\n\n';
    }

    if (structuredContent.position_details) {
        finalContent += '=== POSITION SPECIFICATIONS ===\n';
        finalContent += structuredContent.position_details + '\n\n';
    }

    if (structuredContent.jd_file_info) {
        finalContent += '=== JOB DESCRIPTION DOCUMENT ===\n';
        finalContent += structuredContent.jd_file_info + '\n\n';
    }

    if (structuredContent.rating_criteria) {
        finalContent += '=== RATING CRITERIA ===\n';
        finalContent += structuredContent.rating_criteria + '\n\n';
    }

    // If structured extraction failed, use raw content
    if (!finalContent.trim()) {
        finalContent = structuredContent.raw_content || document.body.innerText;
    }

    console.log('=== CONTENT EXTRACTION DEBUG ===');
    console.log('Applicant info extracted:', !!structuredContent.applicant_info);
    console.log('Position specifications extracted:', !!structuredContent.position_details);
    console.log('JD file info extracted:', !!structuredContent.jd_file_info);
    console.log('Rating criteria extracted:', !!structuredContent.rating_criteria);
    console.log('Final content length:', finalContent.length);
    console.log('Final content preview:', finalContent.substring(0, 500));
    console.log('=== END EXTRACTION DEBUG ===');

    return finalContent;
}

// AI Assistant Functions
function analyzeApplicant() {
    const spinner = document.getElementById('aiLoadingSpinner');
    const resultDiv = document.getElementById('aiAnalysisResult');
    const analyzeBtn = document.getElementById('analyzeApplicantBtn');

    // Show loading state
    spinner.style.display = 'block';
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';

    // Extract page content for AI analysis
    const pageContent = extractPageContent();

    // Make AJAX call to analyze applicant
    fetch(`<?= base_url('ai/analyze') ?>`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': '<?= csrf_hash() ?>'
        },
        body: JSON.stringify({
            page_content: pageContent,
            applicant_id: <?= $application['id'] ?>,
            position_id: <?= $position['id'] ?>,
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        // Hide loading state
        spinner.style.display = 'none';
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Analyze Applicant';

        // Debug logging
        console.log('AI Response received:', data);

        if (data.error) {
            console.error('AI Analysis Error:', data.error);
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Analysis Error:</strong> ${data.error}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
            resultDiv.style.display = 'block';
            return;
        }

        // Store analysis data globally for use in other functions
        window.aiAnalysisData = data;

        // Show results
        displayAiAnalysis(data);
        resultDiv.style.display = 'block';
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        spinner.style.display = 'none';
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Analyze Applicant';

        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Connection Error:</strong> Failed to analyze applicant.
                <br><small>Error: ${error.message}</small>
                <br><small>Please check your internet connection and try again.</small>
            </div>
        `;
        resultDiv.style.display = 'block';
    });
}

function displayAiAnalysis(data) {
    const analysisContent = document.getElementById('aiAnalysisContent');
    const suggestedRatings = document.getElementById('aiSuggestedRatings');

    // Build factual analysis observations
    let observationsHtml = '<div class="mb-3"><strong>Factual Analysis:</strong>';
    if (data.keyObservations && data.keyObservations.length > 0) {
        observationsHtml += '<ul class="mt-2 mb-0">';
        data.keyObservations.forEach(observation => {
            observationsHtml += `<li>${observation}</li>`;
        });
        observationsHtml += '</ul>';
    } else {
        observationsHtml += '<p class="mt-2 mb-0">No factual analysis available.</p>';
    }
    observationsHtml += '</div>';

    // Add rating analysis if available
    let ratingAnalysisHtml = '';
    if (data.ratingAnalysis) {
        ratingAnalysisHtml = `
            <div class="mb-3">
                <strong>Rating Analysis:</strong>
                <div class="mt-2">
                    <div class="mb-2">
                        <small class="text-muted">Scoring System: ${data.ratingAnalysis.scoringSystem || 'Standard scoring'}</small>
                    </div>
        `;

        if (data.ratingAnalysis.criteriaFound && data.ratingAnalysis.criteriaFound.length > 0) {
            ratingAnalysisHtml += `
                <div class="mb-2">
                    <strong>Criteria Identified:</strong>
                    <ul class="mb-0 mt-1">
            `;
            data.ratingAnalysis.criteriaFound.forEach(criterion => {
                ratingAnalysisHtml += `<li>${criterion}</li>`;
            });
            ratingAnalysisHtml += `</ul></div>`;
        }

        if (data.ratingAnalysis.recommendations && data.ratingAnalysis.recommendations.length > 0) {
            ratingAnalysisHtml += `
                <div class="mb-2">
                    <strong>Scoring Recommendations:</strong>
                    <div class="mt-1">
            `;
            data.ratingAnalysis.recommendations.forEach(rec => {
                ratingAnalysisHtml += `
                    <div class="border-start border-primary ps-2 mb-2">
                        <strong>${rec.criterion}:</strong> ${rec.suggestedScore}<br>
                        <small class="text-muted">${rec.reasoning}</small>
                    </div>
                `;
            });
            ratingAnalysisHtml += `</div></div>`;
        }

        ratingAnalysisHtml += `</div></div>`;
    }

    // Add overall recommendation
    let recommendationHtml = '';
    if (data.recommendation) {
        recommendationHtml = `
            <div class="mb-3">
                <strong>Overall Recommendation:</strong>
                <div class="mt-2">
                    <div class="alert alert-${getRecommendationClass(data.overallScore)} py-2">
                        <strong>Score: ${data.overallScore}%</strong> - ${data.recommendation}
                    </div>
                </div>
            </div>
        `;
    }

    analysisContent.innerHTML = observationsHtml + ratingAnalysisHtml + recommendationHtml;

    // Build suggested ratings
    let ratingsHtml = '';
    if (data.profileMatch) {
        Object.keys(data.profileMatch).forEach(key => {
            const match = data.profileMatch[key];
            if (match.score !== undefined && match.maxScore !== undefined) {
                const percentage = (match.score / match.maxScore) * 100;
                const progressClass = getProgressBarClass(percentage);
                ratingsHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="card bg-light">
                            <div class="card-body p-2">
                                <small><strong>${capitalizeFirst(key)}:</strong> ${match.score}/${match.maxScore}</small>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar ${progressClass}" style="width: ${percentage}%"></div>
                                </div>
                                <div class="mt-1">
                                    <small class="text-primary fw-semibold">${match.reasoning || 'No reasoning provided'}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        });
    }

    suggestedRatings.innerHTML = ratingsHtml;
}

// Helper functions for styling
function getBadgeClass(match) {
    switch(match.toLowerCase()) {
        case 'excellent': return 'bg-success';
        case 'good': return 'bg-primary';
        case 'average': case 'fair': return 'bg-warning';
        case 'poor': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getPercentageBadgeClass(percentage) {
    if (percentage >= 80) return 'bg-success';
    if (percentage >= 60) return 'bg-primary';
    if (percentage >= 40) return 'bg-warning';
    return 'bg-danger';
}

function getRecommendationClass(score) {
    if (score >= 80) return 'success';
    if (score >= 60) return 'primary';
    if (score >= 40) return 'warning';
    return 'danger';
}

function getProgressBarClass(percentage) {
    if (percentage >= 80) return 'bg-success';
    if (percentage >= 60) return 'bg-primary';
    if (percentage >= 40) return 'bg-warning';
    return 'bg-danger';
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function applyAiRatings() {
    if (!window.aiAnalysisData || !window.aiAnalysisData.profileMatch) {
        alert('No AI analysis data available. Please run analysis first.');
        return;
    }

    const data = window.aiAnalysisData;
    let appliedCount = 0;
    let remarksArray = [];

    // Apply suggestions based on analysis data
    Object.keys(data.profileMatch).forEach(function(key) {
        const match = data.profileMatch[key];
        if (match.itemId && match.score !== undefined) {
            const select = document.querySelector(`select[name="ratings[${match.itemId}]"]`);
            if (select) {
                select.value = match.score;
                // Trigger change event to update total score
                select.dispatchEvent(new Event('change'));
                appliedCount++;

                // Add detailed reasoning to remarks array
                if (match.reasoning) {
                    const criterionName = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    remarksArray.push(`${criterionName} (Score: ${match.score}): ${match.reasoning}`);
                }
            }
        }
    });

    // Also include scoring recommendations from ratingAnalysis if available
    if (data.ratingAnalysis && data.ratingAnalysis.recommendations) {
        data.ratingAnalysis.recommendations.forEach(function(rec) {
            if (rec.criterion && rec.suggestedScore && rec.reasoning) {
                remarksArray.push(`${rec.criterion} (Suggested: ${rec.suggestedScore}): ${rec.reasoning}`);
            }
        });
    }

    // Include overall recommendation if available
    if (data.recommendation && data.overallScore !== undefined) {
        remarksArray.push(`Overall Assessment (Score: ${data.overallScore}%): ${data.recommendation}`);
    }

    // Update remarks textarea with AI reasoning
    const remarksTextarea = document.querySelector('textarea[name="remarks"]');
    if (remarksTextarea && remarksArray.length > 0) {
        const existingRemarks = remarksTextarea.value.trim();
        const aiRemarks = remarksArray.join('\n• ');

        if (existingRemarks) {
            remarksTextarea.value = existingRemarks + '\n\n=== AI ANALYSIS ===\n• ' + aiRemarks;
        } else {
            remarksTextarea.value = '=== AI ANALYSIS ===\n• ' + aiRemarks;
        }
    }

    // Show success message
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show mt-3';
    alert.innerHTML = `
        <i class="fas fa-check me-2"></i>AI suggestions have been applied to ${appliedCount} rating criteria with reasoning added to remarks.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const cardBody = document.querySelector('#aiAnalysisResult').closest('.card-body');
    cardBody.appendChild(alert);

    // Auto-dismiss after 3 seconds
    setTimeout(function() {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}

function clearAiAnalysis() {
    const resultDiv = document.getElementById('aiAnalysisResult');
    resultDiv.style.display = 'none';

    // Clear any applied ratings (optional)
    const confirmed = confirm('Do you want to clear the applied AI ratings from the form as well?');
    if (confirmed) {
        const selects = document.querySelectorAll('.rating-select');
        selects.forEach(function(select) {
            select.value = '';
            select.dispatchEvent(new Event('change'));
        });
    }
}
</script>


<?= $this->endSection() ?>
