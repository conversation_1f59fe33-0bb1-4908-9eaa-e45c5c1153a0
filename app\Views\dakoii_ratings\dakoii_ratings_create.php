<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Add Rating Item</h5>
        <a href="<?= base_url('dakoii/rating_items/list') ?>" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
    <div class="card-body">
        <form action="<?= base_url('dakoii/rating_items/create') ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="mb-3">
                <label for="item_label" class="form-label">Item Label</label>
                <input type="text" class="form-control" id="item_label" name="item_label" value="<?= old('item_label') ?>" required>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['item_label'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['item_label'] ?></div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Enter a description for this rating item (optional)"><?= old('description') ?></textarea>
                <?php if (session()->has('error') && is_array(session('error')) && isset(session('error')['description'])) : ?>
                    <div class="text-danger mt-2"><?= session('error')['description'] ?></div>
                <?php endif; ?>
                <div class="form-text">Provide a description of what this rating item represents.</div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Item
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?> 