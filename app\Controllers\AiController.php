<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class AiController extends ResourceController
{
    protected $format = 'json';

    /**
     * POST: Analyze applicant page content using Gemini AI (RESTful approach)
     */
    public function analyze()
    {
        log_message('info', '=== AI CONTROLLER ANALYZE START ===');

        try {
            // Get JSON data from request body
            $input = $this->request->getJSON(true);

            // Also try to get from POST data as fallback
            if (empty($input)) {
                $input = [
                    'page_content' => $this->request->getPost('page_content'),
                    'applicant_id' => $this->request->getPost('applicant_id'),
                    'position_id' => $this->request->getPost('position_id')
                ];
            }

            $pageContent = $input['page_content'] ?? '';
            $applicantId = $input['applicant_id'] ?? null;
            $positionId = $input['position_id'] ?? null;

            log_message('info', 'Page content received: ' . (!empty($pageContent) ? 'Yes (' . strlen($pageContent) . ' chars)' : 'No'));
            log_message('info', 'Applicant ID: ' . ($applicantId ?? 'Not provided'));
            log_message('info', 'Position ID: ' . ($positionId ?? 'Not provided'));
            log_message('info', 'Page content preview: ' . substr($pageContent, 0, 200));

            if (empty($pageContent) || trim($pageContent) === '') {
                log_message('error', 'Empty page content received');
                return $this->respond([
                    'error' => 'Page content is empty or not provided'
                ]);
            }

            // Build AI analysis prompt
            $aiPrompt = $this->buildAnalysisPrompt($pageContent);

            log_message('info', 'Calling Gemini AI with prompt length: ' . strlen($aiPrompt));

            // Call Gemini AI
            $aiResponse = $this->callGeminiAI($aiPrompt);

            // Parse AI response into structured format
            $analysisResult = $this->parseAIResponse($aiResponse);

            log_message('info', 'AI analysis completed successfully');
            log_message('info', '=== AI CONTROLLER ANALYZE END ===');

            return $this->respond($analysisResult);

        } catch (\Exception $e) {
            log_message('error', 'AI Controller Exception: ' . $e->getMessage());
            log_message('error', 'Exception trace: ' . $e->getTraceAsString());

            return $this->respond([
                'error' => 'AI analysis failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Build AI analysis prompt from page content
     */
    private function buildAnalysisPrompt($pageContent)
    {
        $prompt = "You are an expert HR analyst specializing in applicant evaluation and rating. Analyze the applicant rating page content below and provide detailed, factual analysis with specific scoring recommendations.\n\n";

        $prompt .= "CRITICAL INSTRUCTIONS:\n";
        $prompt .= "1. CAREFULLY extract all applicant information (name, age, education, work experience, skills, qualifications)\n";
        $prompt .= "2. IDENTIFY the position requirements and job description details\n";
        $prompt .= "3. ANALYZE the specific rating criteria and their scoring options provided in the page\n";
        $prompt .= "4. MATCH the applicant's profile against each rating criterion\n";
        $prompt .= "5. PROVIDE specific scores based on the actual scoring options available (not generic 1-10 scales)\n";
        $prompt .= "6. JUSTIFY each score with factual evidence from the applicant's profile\n";
        $prompt .= "7. FOCUS on objective, measurable qualifications rather than assumptions\n\n";

        $prompt .= "IMPORTANT: Look for the 'RATING CRITERIA' section in the page content to understand the exact scoring system being used. Use the actual score values and labels provided, not generic scores.\n\n";

        $prompt .= "PROVIDE ANALYSIS IN THIS EXACT JSON FORMAT:\n";
        $prompt .= "{\n";
        $prompt .= '  "keyObservations": [';
        $prompt .= '    "Specific factual observation about applicant qualifications",';
        $prompt .= '    "Detailed analysis of work experience relevance",';
        $prompt .= '    "Assessment of educational background match",';
        $prompt .= '    "Skills and competencies evaluation",';
        $prompt .= '    "Overall suitability for the position"';
        $prompt .= '  ],';
        $prompt .= '  "ratingAnalysis": {';
        $prompt .= '    "criteriaFound": ["list of rating criteria identified"],';
        $prompt .= '    "scoringSystem": "description of the scoring system found",';
        $prompt .= '    "recommendations": [';
        $prompt .= '      {"criterion": "criterion name", "suggestedScore": "actual score value", "reasoning": "detailed justification"},';
        $prompt .= '      {"criterion": "criterion name", "suggestedScore": "actual score value", "reasoning": "detailed justification"}';
        $prompt .= '    ]';
        $prompt .= '  },';
        $prompt .= '  "profileMatch": {';
        $prompt .= '    "strengths": ["list of applicant strengths"],';
        $prompt .= '    "concerns": ["list of potential concerns or gaps"],';
        $prompt .= '    "overallFit": "assessment of overall fit for the position"';
        $prompt .= '  },';
        $prompt .= '  "overallScore": 0,';
        $prompt .= '  "recommendation": "Clear recommendation with justification"';
        $prompt .= "}\n\n";

        $prompt .= "APPLICANT RATING PAGE CONTENT:\n";
        $prompt .= "=================================\n";
        $prompt .= $pageContent;
        $prompt .= "\n=================================\n\n";

        $prompt .= "ANALYSIS REQUIREMENTS:\n";
        $prompt .= "- Base all observations on factual information from the page content\n";
        $prompt .= "- Use the exact scoring values and criteria found in the rating system\n";
        $prompt .= "- Provide specific, actionable recommendations\n";
        $prompt .= "- Justify all scores with evidence from the applicant's profile\n";
        $prompt .= "- Be objective and professional in your assessment\n";

        return $prompt;
    }

    /**
     * Parse AI response into structured format expected by the rating form
     */
    private function parseAIResponse($aiResponse)
    {
        log_message('info', 'Parsing AI response: ' . substr($aiResponse, 0, 200) . '...');

        // Clean the response - remove markdown formatting and extra whitespace
        $cleanResponse = preg_replace('/```json\s*|\s*```/', '', $aiResponse);
        $cleanResponse = trim($cleanResponse);

        // Try to extract JSON from AI response
        $jsonMatch = null;
        if (preg_match('/\{[\s\S]*\}/', $cleanResponse, $matches)) {
            $jsonMatch = $matches[0];
            log_message('info', 'JSON found in response: ' . substr($jsonMatch, 0, 200) . '...');
        }

        if ($jsonMatch) {
            try {
                $parsed = json_decode($jsonMatch, true);
                if ($parsed && (isset($parsed['keyObservations']) || isset($parsed['ratingAnalysis']))) {
                    log_message('info', 'Successfully parsed AI JSON response');

                    // Transform the new structure to be compatible with the frontend
                    $transformedResponse = [
                        'keyObservations' => $parsed['keyObservations'] ?? [],
                        'ratingAnalysis' => $parsed['ratingAnalysis'] ?? null,
                        'profileMatch' => $parsed['profileMatch'] ?? [],
                        'overallScore' => $parsed['overallScore'] ?? 0,
                        'recommendation' => $parsed['recommendation'] ?? 'Analysis completed',
                        'rawResponse' => $aiResponse
                    ];

                    // If we have rating recommendations, add them to profileMatch for compatibility
                    if (isset($parsed['ratingAnalysis']['recommendations'])) {
                        // Get actual rating items to map recommendations
                        $ratingItemsModel = new \App\Models\RateItemsModel();
                        $ratingItems = $ratingItemsModel->findAll();

                        foreach ($parsed['ratingAnalysis']['recommendations'] as $rec) {
                            $criterion = $rec['criterion'] ?? '';
                            $itemId = null;

                            // Try to match criterion name with actual rating items
                            foreach ($ratingItems as $item) {
                                $itemLabel = strtolower($item['item_label']);
                                $criterionLower = strtolower($criterion);

                                // Simple matching - if criterion contains item label or vice versa
                                if (strpos($criterionLower, $itemLabel) !== false || strpos($itemLabel, $criterionLower) !== false) {
                                    $itemId = $item['id'];
                                    break;
                                }
                            }

                            $key = strtolower(str_replace(' ', '_', $criterion));
                            $transformedResponse['profileMatch'][$key] = [
                                'itemId' => $itemId, // Add the actual item ID
                                'score' => $rec['suggestedScore'] ?? 0,
                                'maxScore' => 10, // Default, should be extracted from actual criteria
                                'reasoning' => $rec['reasoning'] ?? 'No reasoning provided',
                                'match' => $this->getMatchLevel($rec['suggestedScore'] ?? 0, 10)
                            ];
                        }
                    }

                    return $transformedResponse;
                }
            } catch (\Exception $e) {
                log_message('error', 'Failed to parse AI JSON response: ' . $e->getMessage());
            }
        }

        // Fallback: Create basic structure from text response
        log_message('info', 'Using fallback response structure');
        return [
            'keyObservations' => [
                'AI analysis completed based on page content',
                'Please review the detailed analysis below',
                'Recommendations provided based on applicant profile and position requirements'
            ],
            'ratingAnalysis' => [
                'criteriaFound' => ['General assessment'],
                'scoringSystem' => 'Standard 1-10 scale',
                'recommendations' => []
            ],
            'profileMatch' => [
                'general_assessment' => [
                    'score' => 7,
                    'maxScore' => 10,
                    'reasoning' => 'Analysis completed based on available information',
                    'match' => 'Good'
                ]
            ],
            'overallScore' => 70,
            'recommendation' => 'Analysis completed - please review the detailed feedback',
            'rawResponse' => $aiResponse
        ];
    }

    /**
     * Get match level based on score and max score
     */
    private function getMatchLevel($score, $maxScore)
    {
        if ($maxScore == 0) return 'Unknown';

        $percentage = ($score / $maxScore) * 100;

        if ($percentage >= 80) return 'Excellent';
        if ($percentage >= 70) return 'Good';
        if ($percentage >= 60) return 'Fair';
        if ($percentage >= 40) return 'Poor';
        return 'Very Poor';
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAI($prompt)
    {
        $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={$apiKey}";

        $data = [
            'contents' => [['parts' => [['text' => $prompt]]]],
            'generationConfig' => [
                'maxOutputTokens' => 8192,
                'temperature' => 0.3
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception("Gemini API Error: " . $httpCode);
        }

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return $result['candidates'][0]['content']['parts'][0]['text'];
        } else {
            throw new \Exception("No response from Gemini AI");
        }
    }
}
