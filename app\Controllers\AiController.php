<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class AiController extends ResourceController
{
    protected $format = 'json';

    /**
     * POST: Analyze applicant page content using Gemini AI (RESTful approach)
     */
    public function analyze()
    {
        log_message('info', '=== AI CONTROLLER ANALYZE START ===');

        try {
            // Get page content from request
            $pageContent = $this->request->getPost('page_content');

            log_message('info', 'Page content received: ' . (!empty($pageContent) ? 'Yes (' . strlen($pageContent) . ' chars)' : 'No'));
            log_message('info', 'Page content preview: ' . substr($pageContent, 0, 200));

            if (empty($pageContent) || trim($pageContent) === '') {
                log_message('error', 'Empty page content received');
                return $this->respond([
                    'error' => 'Page content is empty or not provided'
                ]);
            }

            // Build AI analysis prompt
            $aiPrompt = $this->buildAnalysisPrompt($pageContent);

            log_message('info', 'Calling Gemini AI with prompt length: ' . strlen($aiPrompt));

            // Call Gemini AI
            $aiResponse = $this->callGeminiAI($aiPrompt);

            // Parse AI response into structured format
            $analysisResult = $this->parseAIResponse($aiResponse);

            log_message('info', 'AI analysis completed successfully');
            log_message('info', '=== AI CONTROLLER ANALYZE END ===');

            return $this->respond($analysisResult);

        } catch (\Exception $e) {
            log_message('error', 'AI Controller Exception: ' . $e->getMessage());
            log_message('error', 'Exception trace: ' . $e->getTraceAsString());

            return $this->respond([
                'error' => 'AI analysis failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Build AI analysis prompt from page content
     */
    private function buildAnalysisPrompt($pageContent)
    {
        $prompt = "You are an expert HR analyst. Analyze the applicant rating page content below and provide detailed factual analysis.\n\n";

        $prompt .= "INSTRUCTIONS:\n";
        $prompt .= "1. Extract applicant information (name, age, education, experience, skills, etc.)\n";
        $prompt .= "2. Extract position requirements and details\n";
        $prompt .= "3. Extract rating criteria and scoring options\n";
        $prompt .= "4. Analyze how well the applicant matches the position requirements\n";
        $prompt .= "5. Provide specific, factual observations based on the data\n";
        $prompt .= "6. Suggest appropriate ratings for each criteria\n\n";

        $prompt .= "PROVIDE ANALYSIS IN THIS EXACT JSON FORMAT:\n";
        $prompt .= "{\n";
        $prompt .= '  "keyObservations": [';
        $prompt .= '    "Specific factual observation about applicant",';
        $prompt .= '    "Another observation about qualifications or experience",';
        $prompt .= '    "Observation about match with position requirements"';
        $prompt .= '  ],';
        $prompt .= '  "profileMatch": {';
        $prompt .= '    "age": {"score": 0, "maxScore": 10, "reasoning": "detailed analysis", "match": "Good"},';
        $prompt .= '    "qualification": {"score": 0, "maxScore": 10, "reasoning": "detailed analysis", "match": "Good"},';
        $prompt .= '    "experience": {"score": 0, "maxScore": 10, "reasoning": "detailed analysis", "match": "Good"},';
        $prompt .= '    "skills": {"score": 0, "maxScore": 10, "reasoning": "detailed analysis", "match": "Good"}';
        $prompt .= '  },';
        $prompt .= '  "overallScore": 75,';
        $prompt .= '  "recommendation": "Recommended - Good overall match"';
        $prompt .= "}\n\n";

        $prompt .= "PAGE CONTENT TO ANALYZE:\n";
        $prompt .= "=================================\n";
        $prompt .= $pageContent;
        $prompt .= "\n=================================\n\n";

        $prompt .= "Analyze thoroughly and provide specific, factual observations based on the page content provided.";

        return $prompt;
    }

    /**
     * Parse AI response into structured format expected by the rating form
     */
    private function parseAIResponse($aiResponse)
    {
        log_message('info', 'Parsing AI response: ' . substr($aiResponse, 0, 200) . '...');

        // Try to extract JSON from AI response
        $jsonMatch = null;
        if (preg_match('/\{[\s\S]*\}/', $aiResponse, $matches)) {
            $jsonMatch = $matches[0];
            log_message('info', 'JSON found in response: ' . substr($jsonMatch, 0, 200) . '...');
        }

        if ($jsonMatch) {
            try {
                $parsed = json_decode($jsonMatch, true);
                if ($parsed && isset($parsed['keyObservations'])) {
                    log_message('info', 'Successfully parsed AI JSON response');
                    return $parsed;
                }
            } catch (\Exception $e) {
                log_message('error', 'Failed to parse AI JSON response: ' . $e->getMessage());
            }
        }

        // Fallback: Create basic structure from text response
        log_message('info', 'Using fallback response structure');
        return [
            'keyObservations' => [
                'AI analysis completed based on page content',
                'Please review the detailed analysis below',
                'Recommendations provided based on applicant profile and position requirements'
            ],
            'profileMatch' => [
                'general_assessment' => [
                    'score' => 7,
                    'maxScore' => 10,
                    'reasoning' => 'Analysis completed based on available information',
                    'match' => 'Good'
                ]
            ],
            'overallScore' => 70,
            'recommendation' => 'Analysis completed - please review the detailed feedback',
            'rawResponse' => $aiResponse
        ];
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAI($prompt)
    {
        $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={$apiKey}";

        $data = [
            'contents' => [['parts' => [['text' => $prompt]]]],
            'generationConfig' => [
                'maxOutputTokens' => 8192,
                'temperature' => 0.3
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception("Gemini API Error: " . $httpCode);
        }

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return $result['candidates'][0]['content']['parts'][0]['text'];
        } else {
            throw new \Exception("No response from Gemini AI");
        }
    }
}
