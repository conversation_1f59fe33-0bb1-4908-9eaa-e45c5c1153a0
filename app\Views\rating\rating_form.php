<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Rate Application</h2>
                    <p class="text-muted mb-0">
                        Applicant: <?= esc($application['fname']) ?> <?= esc($application['lname']) ?> |
                        Position: <?= esc($position['designation']) ?>
                    </p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>" class="text-decoration-none">
                                <?= esc($exercise['exercise_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/positions/' . $positionGroup['id']) ?>" class="text-decoration-none">
                                <?= esc($positionGroup['group_name']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('rating/applications/' . $position['id']) ?>" class="text-decoration-none">
                                <?= esc($position['designation']) ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Rate</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Position Specifications -->
        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Position Specifications
                    </h5>
                </div>
                <div class="card-body" id="position-details">
                    <h4><?= esc($position['designation']) ?></h4>
                    <p><strong>Reference:</strong> <?= esc($position['position_reference']) ?></p>
                    <p><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                    <p><strong>Qualifications:</strong> <?= nl2br(esc($position['qualifications'] ?? 'Not specified')) ?></p>
                    <p><strong>Knowledge:</strong> <?= nl2br(esc($position['knowledge'] ?? 'Not specified')) ?></p>
                    <p><strong>Skills and Competencies:</strong> <?= nl2br(esc($position['skills_competencies'] ?? 'Not specified')) ?></p>
                    <p><strong>Job Experiences:</strong> <?= nl2br(esc($position['job_experiences'] ?? 'Not specified')) ?></p>
                </div>
            </div>
        </div>

        <!-- Applicant Specifications -->
        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        Applicant Specifications
                    </h5>
                </div>
                <div class="card-body" id="applicant-details">
                    <h4><?= esc($application['fname']) ?> <?= esc($application['lname']) ?></h4>

                    <?php
                    // Parse profile_details JSON if it exists
                    $profileDetails = [];
                    if (!empty($application['profile_details'])) {
                        try {
                            $profileDetails = json_decode($application['profile_details'], true);
                        } catch (Exception $e) {
                            $profileDetails = [];
                        }
                    }

                    if (!empty($profileDetails) && isset($profileDetails['profile_data'])):
                        // Display structured profile data
                        $profileData = $profileDetails['profile_data'];
                    ?>
                        <!-- Core Identifiers -->
                        <?php if(isset($profileData['core_identifiers'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Personal Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Gender:</strong> <?= esc($profileData['core_identifiers']['sex'] ?? $application['gender']) ?></p>
                                    <p><strong>Age:</strong> <?= esc($profileData['core_identifiers']['age'] ?? 'Not specified') ?></p>
                                    <p><strong>Date of Birth:</strong> <?= isset($application['dobirth']) ? date('d M Y', strtotime($application['dobirth'])) : 'Not specified' ?></p>
                                    <p><strong>Place of Origin:</strong> <?= esc($profileData['core_identifiers']['place_of_origin'] ?? $application['place_of_origin'] ?? 'Not specified') ?></p>
                                    <p><strong>Citizenship:</strong> <?= esc($profileData['core_identifiers']['citizenship'] ?? $application['citizenship'] ?? 'Not specified') ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Address:</strong> <?= esc($profileData['core_identifiers']['address_location'] ?? $application['location_address'] ?? 'Not specified') ?></p>
                                    <p><strong>Contact:</strong> <?= nl2br(esc($profileData['core_identifiers']['contact_details'] ?? $application['contact_details'] ?? 'Not specified')) ?></p>
                                    <p><strong>ID Numbers:</strong> <?= nl2br(esc($profileData['core_identifiers']['nid_number'] ?? $application['id_numbers'] ?? 'Not specified')) ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Employment Information -->
                        <?php if(isset($profileData['employment_information'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Employment</h5>
                            <p><strong>Current Position:</strong> <?= esc($profileData['employment_information']['current_position'] ?? $application['current_position']) ?></p>
                            <p><strong>Current Employer:</strong> <?= esc($profileData['employment_information']['current_employer'] ?? $application['current_employer']) ?></p>
                            <p><strong>Current Salary:</strong> <?= esc($profileData['employment_information']['current_salary'] ?? $application['current_salary'] ?? 'Not specified') ?></p>
                            <p><strong>Public Service Status:</strong> <?= esc($profileData['employment_information']['public_service_status'] ?? 'Not specified') ?></p>
                        <?php endif; ?>

                        <!-- Education & Qualifications -->
                        <?php if(isset($profileData['education_training']) && isset($profileData['education_training']['qualifications']) && !empty($profileData['education_training']['qualifications'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Education</h5>
                            <?php foreach($profileData['education_training']['qualifications'] as $index => $qualification):
                                if($index > 2) break; // Show max 3 qualifications
                            ?>
                                <p><strong><?= esc($qualification['level']) ?>:</strong> <?= esc($qualification['course']) ?> - <?= esc($qualification['institution']) ?></p>
                            <?php endforeach; ?>

                            <?php if(count($profileData['education_training']['qualifications']) > 3): ?>
                                <p class="text-muted"><em>And <?= count($profileData['education_training']['qualifications']) - 3 ?> more qualification(s)...</em></p>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Work Experience Summary -->
                        <?php if(isset($profileData['experience']) && isset($profileData['experience']['experience_summary'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Experience Summary</h5>
                            <?php $summary = $profileData['experience']['experience_summary']; ?>
                            <div class="row g-2">
                                <?php if(isset($summary['total_years'])): ?>
                                <div class="col-md-6">
                                    <p><strong>Total Experience:</strong> <?= esc($summary['total_years'] ?? 'Not specified') ?> years</p>
                                </div>
                                <?php endif; ?>

                                <?php if(isset($summary['public_experience'])): ?>
                                <div class="col-md-6">
                                    <p><strong>Public Sector:</strong> <?= esc($summary['public_experience'] ?? 'Not specified') ?> years</p>
                                </div>
                                <?php endif; ?>

                                <?php if(isset($summary['private_experience'])): ?>
                                <div class="col-md-6">
                                    <p><strong>Private Sector:</strong> <?= esc($summary['private_experience'] ?? 'Not specified') ?> years</p>
                                </div>
                                <?php endif; ?>

                                <?php if(isset($summary['relevant_experience'])): ?>
                                <div class="col-md-6">
                                    <p><strong>Relevant Experience:</strong> <?= esc($summary['relevant_experience'] ?? 'Not specified') ?> years</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Achievements -->
                        <?php if(isset($profileData['performance_achievements'])): ?>
                            <?php if(!empty($profileData['performance_achievements']['awards'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Awards</h5>
                            <ul class="mb-3 ps-3">
                                <?php foreach(array_slice($profileData['performance_achievements']['awards'], 0, 3) as $award): ?>
                                <li><?= esc($award) ?></li>
                                <?php endforeach; ?>

                                <?php if(count($profileData['performance_achievements']['awards']) > 3): ?>
                                <li class="text-muted"><em>And <?= count($profileData['performance_achievements']['awards']) - 3 ?> more award(s)...</em></li>
                                <?php endif; ?>
                            </ul>
                            <?php endif; ?>

                            <?php if(!empty($profileData['performance_achievements']['publications'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Publications</h5>
                            <ul class="mb-3 ps-3">
                                <?php foreach(array_slice($profileData['performance_achievements']['publications'], 0, 2) as $publication): ?>
                                <li><?= esc($publication) ?></li>
                                <?php endforeach; ?>

                                <?php if(count($profileData['performance_achievements']['publications']) > 2): ?>
                                <li class="text-muted"><em>And <?= count($profileData['performance_achievements']['publications']) - 2 ?> more publication(s)...</em></li>
                                <?php endif; ?>
                            </ul>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Verification/Referees -->
                        <?php if(isset($profileData['verification']) && !empty($profileData['verification']['referees'])): ?>
                            <h5 class="mt-3 border-bottom pb-1">Referees</h5>
                            <p><?= nl2br(esc($profileData['verification']['referees'])) ?></p>
                        <?php endif; ?>

                    <?php else: ?>
                        <!-- Fallback to basic application data if no profile details found -->
                        <p><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                        <p><strong>Date of Birth:</strong> <?= date('d M Y', strtotime($application['dobirth'])) ?></p>
                        <p><strong>Place of Origin:</strong> <?= esc($application['place_of_origin'] ?? 'Not specified') ?></p>
                        <p><strong>Current Employer:</strong> <?= esc($application['current_employer']) ?></p>
                        <p><strong>Current Position:</strong> <?= esc($application['current_position']) ?></p>
                        <p><strong>Current Salary:</strong> <?= esc($application['current_salary'] ?? 'Not specified') ?></p>
                        <p><strong>Contact Details:</strong> <?= nl2br(esc($application['contact_details'] ?? 'Not specified')) ?></p>
                        <p><strong>ID Numbers:</strong> <?= nl2br(esc($application['id_numbers'] ?? 'Not specified')) ?></p>
                        <p><strong>Citizenship:</strong> <?= esc($application['citizenship'] ?? 'Not specified') ?></p>
                        <?php if (!empty($application['awards'])): ?>
                        <p><strong>Awards:</strong> <?= nl2br(esc($application['awards'])) ?></p>
                        <?php endif; ?>
                        <?php if (!empty($application['publications'])): ?>
                        <p><strong>Publications:</strong> <?= nl2br(esc($application['publications'])) ?></p>
                        <?php endif; ?>
                        <?php if (!empty($application['referees'])): ?>
                        <p><strong>Referees:</strong> <?= nl2br(esc($application['referees'])) ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: Rating Form -->
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        Rating Form
                    </h5>
                </div>
                <div class="card-body" id="rate-applicant-form">
                    <form action="<?= base_url('rating/save') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="application_id" value="<?= $application['id'] ?>">

                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-6">
                                <!-- Age Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_age" class="form-label fw-bold">Age</label>
                                    <select class="form-select" id="rating_age" name="rating_age" required>
                                        <option value="">Select Age Range</option>
                                        <option value="0" <?= (old('rating_age', $application['rating_age'] ?? '') == '0') ? 'selected' : '' ?>>0 | 65+ years</option>
                                        <option value="1" <?= (old('rating_age', $application['rating_age'] ?? '') == '1') ? 'selected' : '' ?>>1 | 60-64 years</option>
                                        <option value="2" <?= (old('rating_age', $application['rating_age'] ?? '') == '2') ? 'selected' : '' ?>>2 | 54-59 years</option>
                                        <option value="3" <?= (old('rating_age', $application['rating_age'] ?? '') == '3') ? 'selected' : '' ?>>3 | 48-53 years</option>
                                        <option value="4" <?= (old('rating_age', $application['rating_age'] ?? '') == '4') ? 'selected' : '' ?>>4 | 42-47 years</option>
                                        <option value="5" <?= (old('rating_age', $application['rating_age'] ?? '') == '5') ? 'selected' : '' ?>>5 | 36-41 years</option>
                                        <option value="6" <?= (old('rating_age', $application['rating_age'] ?? '') == '6') ? 'selected' : '' ?>>6 | 30-35 years</option>
                                        <option value="7" <?= (old('rating_age', $application['rating_age'] ?? '') == '7') ? 'selected' : '' ?>>7 | 24-29 years</option>
                                        <option value="8" <?= (old('rating_age', $application['rating_age'] ?? '') == '8') ? 'selected' : '' ?>>8 | 18-23 years</option>
                                    </select>
                                    <input type="hidden" name="rating_age_max" value="8">
                                </div>

                                <!-- Education Qualification Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_qualification" class="form-label fw-bold">Education Qualification</label>
                                    <select class="form-select" id="rating_qualification" name="rating_qualification" required>
                                        <option value="">Select Education Level</option>
                                        <option value="10" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '10') ? 'selected' : '' ?>>10 | Doctorate</option>
                                        <option value="9" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '9') ? 'selected' : '' ?>>9 | Master's Degree</option>
                                        <option value="8" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '8') ? 'selected' : '' ?>>8 | Honors/Post Graduate Certificate/Diploma</option>
                                        <option value="7" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '7') ? 'selected' : '' ?>>7 | Bachelor's Degree</option>
                                        <option value="6" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '6') ? 'selected' : '' ?>>6 | Advanced Diploma/Associate Degree</option>
                                        <option value="5" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '5') ? 'selected' : '' ?>>5 | Diploma</option>
                                        <option value="4" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '4') ? 'selected' : '' ?>>4 | Certificate 4/Higher Education Certificate</option>
                                        <option value="3" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '3') ? 'selected' : '' ?>>3 | Certificate 3</option>
                                        <option value="2" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '2') ? 'selected' : '' ?>>2 | Certificate 2</option>
                                        <option value="1" <?= (old('rating_qualification', $application['rating_qualification'] ?? '') == '1') ? 'selected' : '' ?>>1 | Certificate 1</option>
                                    </select>
                                    <input type="hidden" name="rating_qualification_max" value="10">
                                </div>

                                <!-- Capability Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_capability" class="form-label fw-bold">Capability Rating</label>
                                    <select class="form-select" id="rating_capability" name="rating_capability" required>
                                        <option value="">Select Capability Level</option>
                                        <option value="5" <?= (old('rating_capability', $application['rating_capability'] ?? '') == '5') ? 'selected' : '' ?>>5 | Very Capable</option>
                                        <option value="4" <?= (old('rating_capability', $application['rating_capability'] ?? '') == '4') ? 'selected' : '' ?>>4 | Above Average</option>
                                        <option value="3" <?= (old('rating_capability', $application['rating_capability'] ?? '') == '3') ? 'selected' : '' ?>>3 | Average/Competent</option>
                                        <option value="2" <?= (old('rating_capability', $application['rating_capability'] ?? '') == '2') ? 'selected' : '' ?>>2 | Low Capability</option>
                                        <option value="1" <?= (old('rating_capability', $application['rating_capability'] ?? '') == '1') ? 'selected' : '' ?>>1 | No Capability</option>
                                    </select>
                                    <input type="hidden" name="rating_capability_max" value="5">
                                </div>

                                <!-- Public Service Status Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_public_service" class="form-label fw-bold">Public Service Status</label>
                                    <select class="form-select" id="rating_public_service" name="rating_public_service" required>
                                        <option value="">Select Status</option>
                                        <option value="3" <?= (old('rating_public_service', $application['rating_public_service'] ?? '') == '3') ? 'selected' : '' ?>>3 | Section 39</option>
                                        <option value="2" <?= (old('rating_public_service', $application['rating_public_service'] ?? '') == '2') ? 'selected' : '' ?>>2 | Public Servant</option>
                                        <option value="1" <?= (old('rating_public_service', $application['rating_public_service'] ?? '') == '1') ? 'selected' : '' ?>>1 | Non-Public Servant</option>
                                    </select>
                                    <input type="hidden" name="rating_public_service_max" value="3">
                                </div>

                                <!-- Skills and Competencies Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_skills_competencies" class="form-label fw-bold">Skills and Competencies Rating</label>
                                    <select class="form-select" id="rating_skills_competencies" name="rating_skills_competencies" required>
                                        <option value="">Select Skills and Competencies Level</option>
                                        <option value="5" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '5') ? 'selected' : '' ?>>5 | Exceptional Skills</option>
                                        <option value="4" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '4') ? 'selected' : '' ?>>4 | Above Average Skills</option>
                                        <option value="3" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '3') ? 'selected' : '' ?>>3 | Average Skills</option>
                                        <option value="2" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '2') ? 'selected' : '' ?>>2 | Below Average Skills</option>
                                        <option value="1" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '1') ? 'selected' : '' ?>>1 | Limited Skills</option>
                                        <option value="0" <?= (old('rating_skills_competencies', $application['rating_skills_competencies'] ?? '') == '0') ? 'selected' : '' ?>>0 | No Skills</option>
                                    </select>
                                    <input type="hidden" name="rating_skills_competencies_max" value="5">
                                </div>

                                <!-- Knowledge Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_knowledge" class="form-label fw-bold">Knowledge Rating</label>
                                    <select class="form-select" id="rating_knowledge" name="rating_knowledge" required>
                                        <option value="">Select Knowledge Level</option>
                                        <option value="5" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '5') ? 'selected' : '' ?>>5 | Exceptional Knowledge</option>
                                        <option value="4" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '4') ? 'selected' : '' ?>>4 | Above Average Knowledge</option>
                                        <option value="3" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '3') ? 'selected' : '' ?>>3 | Average Knowledge</option>
                                        <option value="2" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '2') ? 'selected' : '' ?>>2 | Below Average Knowledge</option>
                                        <option value="1" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '1') ? 'selected' : '' ?>>1 | Limited Knowledge</option>
                                        <option value="0" <?= (old('rating_knowledge', $application['rating_knowledge'] ?? '') == '0') ? 'selected' : '' ?>>0 | No Knowledge</option>
                                    </select>
                                    <input type="hidden" name="rating_knowledge_max" value="5">
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <!-- Private Sector Work Experience (Non-Relevant) -->
                                <div class="form-group mb-3">
                                    <label for="rating_experience_private_non_relevant" class="form-label fw-bold">Private Sector Work Experience (Non-Relevant)</label>
                                    <select class="form-select" id="rating_experience_private_non_relevant" name="rating_experience_private_non_relevant" required>
                                        <option value="">Select Private Non-Relevant Experience</option>
                                        <option value="4" <?= (old('rating_experience_private_non_relevant', $application['rating_experience_private_non_relevant'] ?? '') == '4') ? 'selected' : '' ?>>4 | Private Non-Relevant 20+ years</option>
                                        <option value="3" <?= (old('rating_experience_private_non_relevant', $application['rating_experience_private_non_relevant'] ?? '') == '3') ? 'selected' : '' ?>>3 | Private Non-Relevant 15-19 years</option>
                                        <option value="2" <?= (old('rating_experience_private_non_relevant', $application['rating_experience_private_non_relevant'] ?? '') == '2') ? 'selected' : '' ?>>2 | Private Non-Relevant 10-14 years</option>
                                        <option value="1" <?= (old('rating_experience_private_non_relevant', $application['rating_experience_private_non_relevant'] ?? '') == '1') ? 'selected' : '' ?>>1 | Private Non-Relevant 5-9 years</option>
                                        <option value="0" <?= (old('rating_experience_private_non_relevant', $application['rating_experience_private_non_relevant'] ?? '') == '0') ? 'selected' : '' ?>>0 | No/Less Non-Relevant Exp. 0-4 years</option>
                                    </select>
                                    <input type="hidden" name="rating_experience_private_non_relevant_max" value="4">
                                </div>

                                <!-- Private Sector Work Experience (Relevant) -->
                                <div class="form-group mb-3">
                                    <label for="rating_experience_private_relevant" class="form-label fw-bold">Private Sector Work Experience (Relevant)</label>
                                    <select class="form-select" id="rating_experience_private_relevant" name="rating_experience_private_relevant" required>
                                        <option value="">Select Private Relevant Experience</option>
                                        <option value="5" <?= (old('rating_experience_private_relevant', $application['rating_experience_private_relevant'] ?? '') == '5') ? 'selected' : '' ?>>5 | Private Relevant 20+ years</option>
                                        <option value="4" <?= (old('rating_experience_private_relevant', $application['rating_experience_private_relevant'] ?? '') == '4') ? 'selected' : '' ?>>4 | Private Relevant 15-19 years</option>
                                        <option value="3" <?= (old('rating_experience_private_relevant', $application['rating_experience_private_relevant'] ?? '') == '3') ? 'selected' : '' ?>>3 | Private Relevant 10-14 years</option>
                                        <option value="2" <?= (old('rating_experience_private_relevant', $application['rating_experience_private_relevant'] ?? '') == '2') ? 'selected' : '' ?>>2 | Private Relevant 5-9 years</option>
                                        <option value="1" <?= (old('rating_experience_private_relevant', $application['rating_experience_private_relevant'] ?? '') == '1') ? 'selected' : '' ?>>1 | Private Relevant 0-4 years</option>
                                    </select>
                                    <input type="hidden" name="rating_experience_private_relevant_max" value="5">
                                </div>

                                <!-- Public Sector Work Experience (Non-Relevant) -->
                                <div class="form-group mb-3">
                                    <label for="rating_experience_public_non_relevant" class="form-label fw-bold">Public Sector Work Experience (Non-Relevant)</label>
                                    <select class="form-select" id="rating_experience_public_non_relevant" name="rating_experience_public_non_relevant" required>
                                        <option value="">Select Public Non-Relevant Experience</option>
                                        <option value="5" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '5') ? 'selected' : '' ?>>5 | Public Non-Relevant 20+ years</option>
                                        <option value="4" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '4') ? 'selected' : '' ?>>4 | Public Non-Relevant 15-19 years</option>
                                        <option value="3" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '3') ? 'selected' : '' ?>>3 | Public Non-Relevant 10-14 years</option>
                                        <option value="2" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '2') ? 'selected' : '' ?>>2 | Public Non-Relevant 5-9 years</option>
                                        <option value="1" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '1') ? 'selected' : '' ?>>1 | Public Non-Relevant 4-8 years</option>
                                        <option value="0" <?= (old('rating_experience_public_non_relevant', $application['rating_experience_public_non_relevant'] ?? '') == '0') ? 'selected' : '' ?>>0 | No Public Non-Relevant Exp.</option>
                                    </select>
                                    <input type="hidden" name="rating_experience_public_non_relevant_max" value="5">
                                </div>

                                <!-- Public Sector Work Experience (Relevant) -->
                                <div class="form-group mb-3">
                                    <label for="rating_experience_public_relevant" class="form-label fw-bold">Public Sector Work Experience (Relevant)</label>
                                    <select class="form-select" id="rating_experience_public_relevant" name="rating_experience_public_relevant" required>
                                        <option value="">Select Public Relevant Experience</option>
                                        <option value="5" <?= (old('rating_experience_public_relevant', $application['rating_experience_public_relevant'] ?? '') == '5') ? 'selected' : '' ?>>5 | Public Relevant 20+ years</option>
                                        <option value="4" <?= (old('rating_experience_public_relevant', $application['rating_experience_public_relevant'] ?? '') == '4') ? 'selected' : '' ?>>4 | Public Relevant 15-19 years</option>
                                        <option value="3" <?= (old('rating_experience_public_relevant', $application['rating_experience_public_relevant'] ?? '') == '3') ? 'selected' : '' ?>>3 | Public Relevant 10-14 years</option>
                                        <option value="2" <?= (old('rating_experience_public_relevant', $application['rating_experience_public_relevant'] ?? '') == '2') ? 'selected' : '' ?>>2 | Public Relevant 5-9 years</option>
                                        <option value="1" <?= (old('rating_experience_public_relevant', $application['rating_experience_public_relevant'] ?? '') == '1') ? 'selected' : '' ?>>1 | Public Relevant 0-4 years</option>
                                    </select>
                                    <input type="hidden" name="rating_experience_public_relevant_max" value="5">
                                </div>

                                <!-- Hidden Supervisory Level field -->
                                <input type="hidden" id="rating_supervisory_level" name="rating_supervisory_level" value="<?= old('rating_supervisory_level', $application['rating_supervisory_level'] ?? '0') ?>">
                                <input type="hidden" name="rating_supervisory_level_max" value="5">

                                <!-- Trainings Rating -->
                                <div class="form-group mb-3">
                                    <label for="rating_trainings" class="form-label fw-bold">Trainings Rating</label>
                                    <select class="form-select" id="rating_trainings" name="rating_trainings" required>
                                        <option value="">Select Trainings Level</option>
                                        <option value="5" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '5') ? 'selected' : '' ?>>5 | Extensive Relevant Trainings</option>
                                        <option value="4" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '4') ? 'selected' : '' ?>>4 | Multiple Relevant Trainings</option>
                                        <option value="3" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '3') ? 'selected' : '' ?>>3 | Some Relevant Trainings</option>
                                        <option value="2" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '2') ? 'selected' : '' ?>>2 | Few Relevant Trainings</option>
                                        <option value="1" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '1') ? 'selected' : '' ?>>1 | Limited Trainings</option>
                                        <option value="0" <?= (old('rating_trainings', $application['rating_trainings'] ?? '') == '0') ? 'selected' : '' ?>>0 | No Relevant Trainings</option>
                                    </select>
                                    <input type="hidden" name="rating_trainings_max" value="5">
                                </div>

                                <!-- Hidden Overall Assessment field -->
                                <input type="hidden" id="rating_overall_assessment" name="rating_overall_assessment" value="<?= old('rating_overall_assessment', $application['rating_overall_assessment'] ?? '0') ?>">
                                <input type="hidden" name="rating_overall_assessment_max" value="5">

                            </div>
                        </div>

                        <!-- Comments/Remarks (Full Width) -->
                        <div class="form-group mb-3">
                            <label for="rating_comments" class="form-label fw-bold">Comments/Remarks</label>
                            <textarea class="form-control" id="rating_comments" name="rating_comments" rows="6"><?= old('rating_comments', $application['rating_comments'] ?? '') ?></textarea>
                        </div>

                        <!-- Rating Calculation Section -->
                        <div class="card mt-3 mb-4 shadow-sm border-warning">
                            <div class="card-header bg-warning text-white py-2">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    Rating Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-group">
                                            <label for="rating_total" class="form-label fw-bold">Total Points (Calculated)</label>
                                            <div class="input-group">
                                                <input type="text" id="rating_total" name="rating_total" class="form-control" value="<?= old('rating_total', $application['rating_total'] ?? '0') ?>" readonly>
                                                <span class="input-group-text">/ 55</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-group">
                                            <label for="rating_percentage" class="form-label fw-bold">Percentage (Calculated)</label>
                                            <div class="input-group">
                                                <input type="text" id="rating_percentage" name="rating_percentage" class="form-control" value="<?= old('rating_percentage', $application['rating_percentage'] ?? '0') ?>" readonly>
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('rating/applications/' . $position['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Applications
                            </a>
                            <div>
                                <button type="submit" id="submit-rating" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i> Save Rating
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Column: AI Analysis -->
        <div class="col-md-4">
            <div class="card shadow-sm mb-4 border-info">
                <div class="card-header bg-info bg-opacity-10 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-robot me-2"></i>
                        AI Analysis
                    </h5>
                    <div>
                        <button type="button" id="generateAiAnalysis" class="btn btn-sm btn-primary">
                            <i class="fas fa-brain me-1"></i> AI Analysis
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#aiAnalysisContent">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div id="aiAnalysisContent" class="collapse show">
                    <div class="card-body">
                        <div id="aiAnalysisResult">
                            <p class="text-muted text-center mb-0">Click "AI Analysis" to get automated rating suggestions based on applicant qualifications and position requirements.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Function to calculate total rating
    function calculateRating() {
        let total = 0;
        let maxTotal = 0;

        // Gather all ratings
        const ratings = [
            { value: parseInt($('#rating_age').val() || 0), max: parseInt($('input[name="rating_age_max"]').val()) },
            { value: parseInt($('#rating_qualification').val() || 0), max: parseInt($('input[name="rating_qualification_max"]').val()) },
            { value: parseInt($('#rating_capability').val() || 0), max: parseInt($('input[name="rating_capability_max"]').val()) },
            { value: parseInt($('#rating_public_service').val() || 0), max: parseInt($('input[name="rating_public_service_max"]').val()) },
            { value: parseInt($('#rating_skills_competencies').val() || 0), max: parseInt($('input[name="rating_skills_competencies_max"]').val()) },
            { value: parseInt($('#rating_knowledge').val() || 0), max: parseInt($('input[name="rating_knowledge_max"]').val()) },
            { value: parseInt($('#rating_trainings').val() || 0), max: parseInt($('input[name="rating_trainings_max"]').val()) },
            { value: parseInt($('#rating_experience_private_non_relevant').val() || 0), max: parseInt($('input[name="rating_experience_private_non_relevant_max"]').val()) },
            { value: parseInt($('#rating_experience_private_relevant').val() || 0), max: parseInt($('input[name="rating_experience_private_relevant_max"]').val()) },
            { value: parseInt($('#rating_experience_public_non_relevant').val() || 0), max: parseInt($('input[name="rating_experience_public_non_relevant_max"]').val()) },
            { value: parseInt($('#rating_experience_public_relevant').val() || 0), max: parseInt($('input[name="rating_experience_public_relevant_max"]').val()) }
            // Supervisory Level and Overall Assessment are now hidden fields
        ];

        // Sum values
        ratings.forEach(rating => {
            total += rating.value;
            maxTotal += rating.max;
        });

        // Calculate percentage
        const percentage = maxTotal > 0 ? ((total / maxTotal) * 100).toFixed(2) : 0;

        // Update the UI
        $('#rating_total').val(total);
        $('#rating_percentage').val(percentage);
    }

    // Calculate rating when a selection changes
    $('.form-select').on('change', function() {
        calculateRating();
    });

    // Initial calculation on page load if values exist
    calculateRating();

    // Helper function to extract score from AI analysis text
    function extractScoreFromAnalysis(text, category) {
        // Define score mappings for each category
        const scorePatterns = {
            qualification: {
                "doctorate": 10,
                "master's degree": 9,
                "master": 9,
                "post graduate": 8,
                "honors": 8,
                "bachelor's degree": 7,
                "bachelor": 7,
                "advanced diploma": 6,
                "associate degree": 6,
                "diploma": 5,
                "certificate 4": 4,
                "certificate 3": 3,
                "certificate 2": 2,
                "certificate 1": 1
            },
            experience_private_non_relevant: {
                "20+ years": 4,
                "15-19 years": 3,
                "10-14 years": 2,
                "5-9 years": 1,
                "0-4 years": 0
            },
            experience_private_relevant: {
                "20+ years": 5,
                "15-19 years": 4,
                "10-14 years": 3,
                "5-9 years": 2,
                "0-4 years": 1
            },
            experience_public_non_relevant: {
                "20+ years": 5,
                "15-19 years": 4,
                "10-14 years": 3,
                "5-9 years": 2,
                "4-8 years": 1,
                "0-4 years": 0
            },
            experience_public_relevant: {
                "20+ years": 5,
                "15-19 years": 4,
                "10-14 years": 3,
                "5-9 years": 2,
                "0-4 years": 1
            },
            supervisory_level: {
                "top level": 5,
                "executive": 5,
                "ceo": 5,
                "md": 5,
                "senior management": 4,
                "middle management": 3,
                "team leader": 2,
                "supervisor": 2,
                "individual contributor": 1,
                "no supervisory": 0
            },
            skills_competencies: {
                "exceptional": 5,
                "above average": 4,
                "average": 3,
                "below average": 2,
                "limited": 1,
                "no skills": 0
            },
            knowledge: {
                "exceptional": 5,
                "extensive": 5,
                "above average": 4,
                "good": 4,
                "average": 3,
                "below average": 2,
                "limited": 1,
                "no knowledge": 0
            },
            trainings: {
                "extensive": 5,
                "multiple": 4,
                "some": 3,
                "few": 2,
                "limited": 1,
                "no trainings": 0
            },
            capability: {
                "very capable": 5,
                "highly capable": 5,
                "above average": 4,
                "competent": 3,
                "average": 3,
                "low capability": 2,
                "no capability": 1
            },
            overall_assessment: {
                "exceptional": 5,
                "excellent": 5,
                "above average": 4,
                "average": 3,
                "below average": 2,
                "poor": 1
            }
        };

        // Get the pattern for the category
        const patterns = scorePatterns[category];
        if (!patterns) return null;

        // Lowercase the text for case-insensitive matching
        const lowerText = text.toLowerCase();

        // Try to find matches for the patterns
        for (const [pattern, score] of Object.entries(patterns)) {
            if (lowerText.includes(pattern)) {
                return score;
            }
        }

        return null;
    }

    // Function to parse AI analysis and suggest ratings
    function suggestRatings(analysisText) {
        // Extract rating suggestions using regex
        const ratingPatterns = {
            'rating_age': /Age Consideration[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_qualification': /Qualification Match[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_experience_private_non_relevant': /Private Sector Work Experience \(Non-Relevant\)[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_experience_private_relevant': /Private Sector Work Experience \(Relevant\)[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_experience_public_non_relevant': /Public Sector Work Experience \(Non-Relevant\)[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_experience_public_relevant': /Public Sector Work Experience \(Relevant\)[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_supervisory_level': /Supervisory Level[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_skills_competencies': /Skills and Competencies[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_knowledge': /Knowledge[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_trainings': /Trainings[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_capability': /Capability Rating[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_public_service': /Public Service Status[\s\S]*?Suggested Rating: (\d+)/i,
            'rating_overall_assessment': /Overall Assessment[\s\S]*?Suggested Rating: (\d+)/i
        };

        // Try to apply each suggested rating
        let ratingsFound = 0;
        let changesApplied = [];

        for (const [fieldId, pattern] of Object.entries(ratingPatterns)) {
            const match = analysisText.match(pattern);
            if (match && match[1]) {
                const rating = match[1];
                const $field = $('#' + fieldId);

                // Get current value
                const currentValue = $field.val();

                // Only update if field exists and value is different
                if ($field.length && (!currentValue || currentValue !== rating)) {
                    $field.val(rating);

                    // Store the field that was changed
                    const fieldLabel = $('label[for="' + fieldId + '"]').text();
                    changesApplied.push({
                        field: fieldLabel || fieldId.replace('rating_', '').replace('_', ' '),
                        value: rating,
                        description: $field.find('option[value="' + rating + '"]').text().replace(rating + ' | ', '')
                    });

                    ratingsFound++;

                    // Highlight the changed field
                    $field.addClass('border-primary');
                    setTimeout(() => {
                        $field.removeClass('border-primary');
                    }, 2000);
                }
            }
        }

        // Use the legacy extraction method as fallback
        if (ratingsFound === 0) {
            // Extract key insights from analysis
            const qualificationMatch = analysisText.match(/Qualification Match[\s\S]*?(?=Experience Relevance|\n\n|$)/i);
            const experienceRelevance = analysisText.match(/Experience Relevance[\s\S]*?(?=Skills Assessment|\n\n|$)/i);
            const skillsAssessment = analysisText.match(/Skills Assessment[\s\S]*?(?=Knowledge Assessment|\n\n|$)/i);
            const knowledgeAssessment = analysisText.match(/Knowledge Assessment[\s\S]*?(?=Training Assessment|\n\n|$)/i);
            const trainingAssessment = analysisText.match(/Training Assessment[\s\S]*?(?=Strengths|\n\n|$)/i);
            const overallSuitability = analysisText.match(/Overall Assessment[\s\S]*?(?=\n\n|$)/i);

            // Suggest qualification rating
            if (qualificationMatch) {
                const qualScore = extractScoreFromAnalysis(qualificationMatch[0], 'qualification');
                if (qualScore) {
                    $('#rating_qualification').val(qualScore);
                    changesApplied.push({
                        field: 'Education Qualification',
                        value: qualScore,
                        description: $('#rating_qualification option[value="' + qualScore + '"]').text().replace(qualScore + ' | ', '')
                    });
                }
            }

            // Suggest experience ratings
            if (experienceRelevance) {
                // Check for private sector relevant experience
                const privateRelevantScore = extractScoreFromAnalysis(experienceRelevance[0], 'experience_private_relevant');
                if (privateRelevantScore) {
                    $('#rating_experience_private_relevant').val(privateRelevantScore);
                    changesApplied.push({
                        field: 'Private Sector Work Experience (Relevant)',
                        value: privateRelevantScore,
                        description: $('#rating_experience_private_relevant option[value="' + privateRelevantScore + '"]').text().replace(privateRelevantScore + ' | ', '')
                    });
                }

                // Check for public sector relevant experience
                const publicRelevantScore = extractScoreFromAnalysis(experienceRelevance[0], 'experience_public_relevant');
                if (publicRelevantScore) {
                    $('#rating_experience_public_relevant').val(publicRelevantScore);
                    changesApplied.push({
                        field: 'Public Sector Work Experience (Relevant)',
                        value: publicRelevantScore,
                        description: $('#rating_experience_public_relevant option[value="' + publicRelevantScore + '"]').text().replace(publicRelevantScore + ' | ', '')
                    });
                }

                // Check for supervisory level
                const supervisoryScore = extractScoreFromAnalysis(experienceRelevance[0], 'supervisory_level');
                if (supervisoryScore) {
                    $('#rating_supervisory_level').val(supervisoryScore);
                    changesApplied.push({
                        field: 'Supervisory Level',
                        value: supervisoryScore,
                        description: $('#rating_supervisory_level option[value="' + supervisoryScore + '"]').text().replace(supervisoryScore + ' | ', '')
                    });
                }
            }

            // Suggest skills and capability ratings
            if (skillsAssessment) {
                const skillsScore = extractScoreFromAnalysis(skillsAssessment[0], 'skills_competencies');
                if (skillsScore) {
                    $('#rating_skills_competencies').val(skillsScore);
                    changesApplied.push({
                        field: 'Skills and Competencies',
                        value: skillsScore,
                        description: $('#rating_skills_competencies option[value="' + skillsScore + '"]').text().replace(skillsScore + ' | ', '')
                    });
                }

                const capabilityScore = extractScoreFromAnalysis(skillsAssessment[0], 'capability');
                if (capabilityScore) {
                    $('#rating_capability').val(capabilityScore);
                    changesApplied.push({
                        field: 'Capability Rating',
                        value: capabilityScore,
                        description: $('#rating_capability option[value="' + capabilityScore + '"]').text().replace(capabilityScore + ' | ', '')
                    });
                }
            }

            // Suggest knowledge rating
            if (knowledgeAssessment) {
                const knowledgeScore = extractScoreFromAnalysis(knowledgeAssessment[0], 'knowledge');
                if (knowledgeScore) {
                    $('#rating_knowledge').val(knowledgeScore);
                    changesApplied.push({
                        field: 'Knowledge Rating',
                        value: knowledgeScore,
                        description: $('#rating_knowledge option[value="' + knowledgeScore + '"]').text().replace(knowledgeScore + ' | ', '')
                    });
                }
            }

            // Suggest trainings rating
            if (trainingAssessment) {
                const trainingsScore = extractScoreFromAnalysis(trainingAssessment[0], 'trainings');
                if (trainingsScore) {
                    $('#rating_trainings').val(trainingsScore);
                    changesApplied.push({
                        field: 'Trainings Rating',
                        value: trainingsScore,
                        description: $('#rating_trainings option[value="' + trainingsScore + '"]').text().replace(trainingsScore + ' | ', '')
                    });
                }
            }

            // Suggest overall assessment
            if (overallSuitability) {
                const overallScore = extractScoreFromAnalysis(overallSuitability[0], 'overall_assessment');
                if (overallScore) {
                    $('#rating_overall_assessment').val(overallScore);
                    changesApplied.push({
                        field: 'Overall Assessment',
                        value: overallScore,
                        description: $('#rating_overall_assessment option[value="' + overallScore + '"]').text().replace(overallScore + ' | ', '')
                    });
                }
            }
        }

        // Update the rating total after suggesting values
        calculateRating();

        // Return the changes that were applied
        return changesApplied;
    }

    // AI Analysis - With comprehensive debug logging
    $('#generateAiAnalysis').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Analyzing...');

        // Clear any existing results first
        $('#aiAnalysisResult').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Starting AI analysis...</div>');

        // Get page content
        const pageContent = document.body.innerText;

        console.log('=== AI ANALYSIS DEBUG START ===');
        console.log('1. Button clicked, starting AI analysis...');
        console.log('2. Page content length:', pageContent.length);
        console.log('3. Page content preview (first 500 chars):', pageContent.substring(0, 500));
        console.log('4. AJAX URL:', '<?= base_url('ai/analyze') ?>');
        console.log('5. Sending request...');

        // Direct AJAX call with full debug and cache busting
        $.ajax({
            url: '<?= base_url('ai/analyze') ?>',
            method: 'POST',
            data: {
                prompt: pageContent,
                debug: 'true',
                timestamp: Date.now(), // Cache busting
                applicant_id: <?= $application['id'] ?>,
                position_id: <?= $position['id'] ?>
            },
            cache: false, // Disable caching
            beforeSend: function(xhr) {
                console.log('6. Request headers:', xhr.getAllResponseHeaders());
                $('#aiAnalysisResult').html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Sending request to AI...</div>');
            }
        })
        .done(function(response, textStatus, xhr) {
            console.log('7. AJAX SUCCESS!');
            console.log('8. Response status:', textStatus);
            console.log('9. Response headers:', xhr.getAllResponseHeaders());
            console.log('10. Full response object:', response);
            console.log('11. Response type:', typeof response);
            console.log('12. Response message:', response.message);
            console.log('=== AI ANALYSIS DEBUG END ===');

            if (response && response.message) {
                const timestamp = new Date().toLocaleString();
                $('#aiAnalysisResult').html(`
                    <div class="alert alert-success">
                        <h6><i class="fas fa-robot me-2"></i>Fresh AI Analysis Results</h6>
                        <small class="text-muted">Generated: ${timestamp}</small>
                        <hr>
                        <pre style="white-space: pre-wrap;">${response.message}</pre>
                        ${response.debug_info ? '<hr><small class="text-muted">Debug: Response length: ' + response.debug_info.response_length + ' chars</small>' : ''}
                    </div>
                `);
            } else {
                $('#aiAnalysisResult').html('<div class="alert alert-warning"><h6>No AI Response</h6><p>No message in response: ' + JSON.stringify(response) + '</p></div>');
            }
        })
        .fail(function(xhr, status, error) {
            console.log('=== AJAX FAILURE DEBUG ===');
            console.error('AJAX failed with status:', status);
            console.error('Error:', error);
            console.error('Response status:', xhr.status);
            console.error('Response text:', xhr.responseText);
            console.error('Response headers:', xhr.getAllResponseHeaders());
            console.log('=== AJAX FAILURE DEBUG END ===');

            $('#aiAnalysisResult').html('<div class="alert alert-danger"><h6>AJAX Error:</h6><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p></div>');
        })
        .always(function() {
            button.prop('disabled', false).html('<i class="fas fa-brain"></i> AI Analysis');
        });
    });






});
</script>
<?= $this->endSection() ?>