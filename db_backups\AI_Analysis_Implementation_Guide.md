# AI Analysis Implementation Guide

## Overview
This guide explains how to implement AI-powered analysis functionality similar to the SelMasta rating system. The system uses multiple AI providers (Anthrop<PERSON>, Google Gemini, DeepSeek) to analyze job applicants against position requirements.

## System Architecture

### 1. Frontend Components
- **AI Chat Interface**: Real-time chat window for AI interactions
- **Data Collection**: Automatic extraction of relevant information
- **Clipboard Integration**: Seamless data transfer to AI
- **Loading States**: User feedback during processing

### 2. Backend Components
- **AI Controller**: Handles AI API requests and responses
- **Multi-Provider Support**: Supports multiple AI services
- **Error Handling**: Robust error management
- **Performance Tracking**: Response time monitoring

## Implementation Steps

### Step 1: Database Setup

```sql
-- Add AI model preference to organization table
ALTER TABLE organizations ADD COLUMN ai_model VARCHAR(50) DEFAULT 'anthropic';

-- Possible values: 'anthropic', 'gemini', 'deepseek'
```

### Step 2: Backend Controller (CodeIgniter 4)

```php
<?php
namespace App\Controllers;

use App\Models\OrganizationModel;

class AIController extends BaseController
{
    protected $organizationModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->organizationModel = new OrganizationModel();
    }

    public function aiAnalysis()
    {
        // Start timing
        $startTime = microtime(true);

        // Validate AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ]);
        }

        // Get prompt data
        $prompt = $this->request->getPost('prompt');
        if (empty($prompt)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'No prompt provided'
            ]);
        }

        try {
            // Get organization's AI model preference
            $org = $this->organizationModel->where('id', session('org_id'))->first();
            $aiModel = $org['ai_model'] ?? 'anthropic';

            // Route to appropriate AI service
            $result = null;
            switch ($aiModel) {
                case 'gemini':
                    $result = $this->useGeminiAI($prompt);
                    break;
                case 'deepseek':
                    $result = $this->useDeepSeekAI($prompt);
                    break;
                case 'anthropic':
                default:
                    $result = $this->useAnthropicAI($prompt);
                    break;
            }

            // Calculate processing time
            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            // Add metadata to response
            $decodedResult = json_decode($result->getJSON(), true);
            $decodedResult['processingTime'] = $processingTime;
            $decodedResult['model'] = $aiModel;

            return $this->response->setJSON($decodedResult);

        } catch (\Exception $e) {
            log_message('error', 'AI Analysis Error: ' . $e->getMessage());
            
            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'AI Error: ' . $e->getMessage(),
                'processingTime' => $processingTime,
                'model' => $aiModel ?? 'unknown'
            ]);
        }
    }

    private function useAnthropicAI($prompt)
    {
        $apiKey = 'YOUR_ANTHROPIC_API_KEY';
        $apiUrl = 'https://api.anthropic.com/v1/messages';

        $data = [
            "model" => "claude-3-5-sonnet-20241022",
            "max_tokens" => 2000,
            'temperature' => 0.0,
            "system" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting.",
            "messages" => [
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'x-api-key: ' . $apiKey,
            'anthropic-version: 2023-06-01'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception('API request failed with status: ' . $httpCode);
        }

        $decodedResponse = json_decode($response, true);
        
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        $aiResponse = $decodedResponse['content'][0]['text'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from Anthropic AI');
        }

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }

    private function useGeminiAI($prompt)
    {
        $apiKey = 'YOUR_GEMINI_API_KEY';
        $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/learnlm-1.5-pro-experimental:generateContent';

        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "text" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting. Here is the request: " . $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 2048,
                "stopSequences" => []
            ],
            "safetySettings" => [
                [
                    "category" => "HARM_CATEGORY_HARASSMENT",
                    "threshold" => "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category" => "HARM_CATEGORY_HATE_SPEECH",
                    "threshold" => "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category" => "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold" => "BLOCK_MEDIUM_AND_ABOVE"
                ],
                [
                    "category" => "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold" => "BLOCK_MEDIUM_AND_ABOVE"
                ]
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl . '?key=' . $apiKey);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception('API request failed with status: ' . $httpCode);
        }

        $decodedResponse = json_decode($response, true);
        
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        $aiResponse = $decodedResponse['candidates'][0]['content']['parts'][0]['text'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from Gemini AI');
        }

        // Clean any potential markdown formatting
        $aiResponse = preg_replace('/[*_`#]/', '', $aiResponse);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }

    private function useDeepSeekAI($prompt)
    {
        $apiKey = 'YOUR_DEEPSEEK_API_KEY';
        $apiUrl = 'https://api.deepseek.com/v1/chat/completions';

        $data = [
            "model" => "deepseek-chat",
            "messages" => [
                [
                    "role" => "system",
                    "content" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting."
                ],
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ],
            "temperature" => 0,
            "max_tokens" => 2000,
            "stream" => false
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception('API request failed with status: ' . $httpCode);
        }

        $decodedResponse = json_decode($response, true);
        
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        $aiResponse = $decodedResponse['choices'][0]['message']['content'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from DeepSeek AI');
        }

        // Clean any potential markdown formatting
        $aiResponse = preg_replace('/[*_`#]/', '', $aiResponse);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }
}
```

### Step 3: Routes Configuration

```php
// In app/Config/Routes.php
$routes->group('ai', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->post('analyze', 'AIController::aiAnalysis');
});
```

### Step 4: Frontend HTML Structure

```html
<!-- AI Chat Interface -->
<div class="col-6">
    <div class="card card-primary card-outline mt-3">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-star mr-2"></i>
                AI Analysis Assistant
            </h3>
        </div>
        <div class="card-body">
            <div id="ai-chat-container" class="chat-container" style="height: 500px; overflow-y: auto;">
                <!-- Chat messages will appear here -->
            </div>
            <div class="input-group mt-3">
                <button type="button" class="btn bg-purple" id="analyzeButton">
                    <i class="fas fa-robot mr-1"></i> Analyze Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Data Collection Button -->
<div class="col-12 p-3">
    <button type="button" class="btn bg-purple float-right btn-copy-details" onclick="copyAllDetails()">
        <i class="fas fa-copy"></i> AI Prompt
    </button>
</div>

<!-- Data Sections for Analysis -->
<div id="position-details" class="card-body">
    <!-- Position/Job details content -->
    <p><strong>Title:</strong> <?= esc($position['title']) ?></p>
    <p><strong>Description:</strong> <?= nl2br(esc($position['description'])) ?></p>
    <p><strong>Requirements:</strong> <?= nl2br(esc($position['requirements'])) ?></p>
    <!-- Add more relevant fields -->
</div>

<div id="applicant-details" class="card-body">
    <!-- Applicant/Subject details content -->
    <p><strong>Name:</strong> <?= esc($applicant['name']) ?></p>
    <p><strong>Education:</strong> <?= nl2br(esc($applicant['education'])) ?></p>
    <p><strong>Experience:</strong> <?= nl2br(esc($applicant['experience'])) ?></p>
    <!-- Add more relevant fields -->
</div>

<div id="analysis-form" class="card-body">
    <!-- Form or criteria for analysis -->
    <!-- This contains the rating criteria, evaluation form, etc. -->
</div>
```

### Step 5: CSS Styling

```css
<style>
    .chat-message {
        margin: 10px 0;
        padding: 15px;
        border-radius: 5px;
        max-width: 100%;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        font-family: Arial, sans-serif;
    }

    .user-message {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
    }

    .bot-message {
        background-color: #e9ecef;
        border: 1px solid #dee2e6;
        color: #212529;
    }

    .message-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
        padding-top: 5px;
        border-top: 1px solid #dee2e6;
    }

    .loading-indicator {
        display: none;
        text-align: center;
        padding: 10px;
        color: #6c757d;
    }

    .loading-indicator i {
        margin-right: 5px;
    }

    .chat-container {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        background-color: #ffffff;
    }
</style>
```

### Step 6: JavaScript Implementation

```javascript
<script>
$(document).ready(function() {
    const chatContainer = $('#ai-chat-container');
    const analyzeButton = $('#analyzeButton');
    const remarksTextarea = $('#remarks'); // Optional: auto-fill remarks field

    // Function to add messages to chat
    function addMessage(content, sender, processingTime = null, model = null) {
        const messageDiv = $('<div>')
            .addClass('chat-message')
            .addClass(`${sender}-message`);

        if (sender === 'bot') {
            messageDiv.text(content);

            // Optional: Auto-fill a remarks or analysis field
            if (remarksTextarea.length) {
                remarksTextarea.val(content);
            }

            // Add processing info if available
            if (processingTime && model) {
                const infoDiv = $('<div>')
                    .addClass('message-info')
                    .text(`Processed in ${processingTime}s using ${model}`);
                messageDiv.append(infoDiv);
            }
        } else {
            messageDiv.text(content);
        }

        chatContainer.append(messageDiv);
        chatContainer.scrollTop(chatContainer[0].scrollHeight);
    }

    // Function to show loading indicator
    function showLoading() {
        const loadingDiv = $('<div>').addClass('loading-indicator').html(
            '<i class="fas fa-spinner fa-spin"></i> AI is analyzing...'
        );
        chatContainer.append(loadingDiv);
        loadingDiv.fadeIn();
        chatContainer.scrollTop(chatContainer[0].scrollHeight);
        return loadingDiv;
    }

    // Data collection function
    function copyAllDetails() {
        const positionDiv = document.getElementById('position-details');
        const applicantDiv = document.getElementById('applicant-details');
        const formDiv = document.getElementById('analysis-form');

        // Construct the AI prompt
        const text = "Please analyze the following data and provide insights:\n\n" +
                    "ANALYSIS CRITERIA:\n" +
                    "================\n" +
                    "1. Evaluate relevance and fit\n" +
                    "2. Identify strengths and weaknesses\n" +
                    "3. Provide scoring recommendations\n" +
                    "4. Give brief remarks\n\n" +
                    "POSITION/JOB DETAILS:\n" +
                    "================\n" +
                    positionDiv.innerText +
                    "\n\nSUBJECT/APPLICANT DETAILS:\n" +
                    "================\n" +
                    applicantDiv.innerText +
                    "\n\nEVALUATION FORM:\n" +
                    "================\n" +
                    formDiv.innerText;

        navigator.clipboard.writeText(text).then(() => {
            // Show success feedback
            const btn = document.querySelector('.btn-copy-details');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            setTimeout(() => {
                btn.innerHTML = originalText;
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
        });
    }

    // Make copyAllDetails globally available
    window.copyAllDetails = copyAllDetails;

    // AI Analysis button click handler
    analyzeButton.on('click', function() {
        const loadingIndicator = showLoading();
        analyzeButton.prop('disabled', true);

        // Get the copied text from clipboard
        copyAllDetails();

        // Small delay to ensure clipboard is updated
        setTimeout(function() {
            navigator.clipboard.readText().then(function(clipText) {
                // Add user message
                addMessage("Requesting AI analysis...", "user");

                // Make AJAX call to backend
                $.ajax({
                    url: '<?= base_url('ai/analyze') ?>',
                    method: 'POST',
                    data: {
                        prompt: clipText
                    },
                    success: function(response) {
                        loadingIndicator.remove();
                        if (response.status === 'success') {
                            addMessage(response.message, 'bot', response.processingTime, response.model);
                        } else {
                            addMessage('Error: ' + response.message, 'bot', response.processingTime, response.model);
                        }
                    },
                    error: function() {
                        loadingIndicator.remove();
                        addMessage('Sorry, there was an error processing your request.', 'bot');
                    },
                    complete: function() {
                        analyzeButton.prop('disabled', false);
                    }
                });
            }).catch(function(err) {
                loadingIndicator.remove();
                addMessage('Error: Unable to access clipboard. Please try again.', 'bot');
                analyzeButton.prop('disabled', false);
            });
        }, 100);
    });
});
</script>
```

## AI Prompt Engineering

### Effective Prompt Structure

```text
SYSTEM PROMPT:
"You are a helpful assistant that analyzes [DOMAIN] against [CRITERIA]. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting."

USER PROMPT TEMPLATE:
"Please analyze the following data and provide insights:

ANALYSIS CRITERIA:
================
1. [Criterion 1]
2. [Criterion 2]
3. [Criterion 3]
4. [Criterion 4]

[SECTION 1 TITLE]:
================
[Section 1 Data]

[SECTION 2 TITLE]:
================
[Section 2 Data]

[SECTION 3 TITLE]:
================
[Section 3 Data]

Please provide:
- Overall assessment
- Strengths and weaknesses
- Recommendations
- Brief remarks"
```

### Sample AI Prompts for Different Use Cases

#### 1. Job Applicant Analysis
```text
SYSTEM: "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only."

USER: "Please analyze this job applicant against the position requirements:

EVALUATION CRITERIA:
1. Education qualification match
2. Work experience relevance
3. Skills and competencies
4. Overall suitability

POSITION DETAILS:
[Job title, requirements, responsibilities]

APPLICANT DETAILS:
[Education, experience, skills, background]

Please provide scoring recommendations and brief remarks."
```

#### 2. Document Analysis
```text
SYSTEM: "You are a helpful assistant that analyzes documents for compliance and quality. Provide responses in plain text only."

USER: "Please analyze this document:

ANALYSIS CRITERIA:
1. Completeness
2. Accuracy
3. Compliance with standards
4. Areas for improvement

DOCUMENT CONTENT:
[Document text]

REQUIREMENTS:
[Standards or requirements to check against]"
```

#### 3. Performance Evaluation
```text
SYSTEM: "You are a helpful assistant that evaluates performance data. Provide responses in plain text only."

USER: "Please analyze this performance data:

EVALUATION CRITERIA:
1. Goal achievement
2. Quality metrics
3. Improvement areas
4. Overall rating

PERFORMANCE DATA:
[Metrics, achievements, feedback]

TARGETS/GOALS:
[Expected performance standards]"
```

## Security Considerations

### 1. API Key Management
```php
// Use environment variables for API keys
$apiKey = getenv('ANTHROPIC_API_KEY') ?: 'fallback-key';

// Or use CodeIgniter's .env file
$apiKey = env('ANTHROPIC_API_KEY');
```

### 2. Input Validation
```php
// Validate and sanitize input
$prompt = trim($this->request->getPost('prompt'));
if (strlen($prompt) > 10000) { // Limit prompt size
    throw new \Exception('Prompt too long');
}

// Remove potentially harmful content
$prompt = strip_tags($prompt);
$prompt = htmlspecialchars($prompt, ENT_QUOTES, 'UTF-8');
```

### 3. Rate Limiting
```php
// Implement rate limiting
$userId = session('user_id');
$cacheKey = "ai_requests_{$userId}";
$requests = cache($cacheKey) ?? 0;

if ($requests >= 10) { // 10 requests per hour
    throw new \Exception('Rate limit exceeded');
}

cache($cacheKey, $requests + 1, 3600); // 1 hour
```

### 4. Error Handling
```php
try {
    $result = $this->callAI($prompt);
} catch (\Exception $e) {
    // Log error without exposing sensitive data
    log_message('error', 'AI Error: ' . $e->getMessage());

    return $this->response->setJSON([
        'status' => 'error',
        'message' => 'Analysis temporarily unavailable'
    ]);
}
```

## Deployment and Configuration

### 1. Environment Setup
```bash
# .env file
ANTHROPIC_API_KEY=your_anthropic_key_here
GEMINI_API_KEY=your_gemini_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here

# Database configuration
AI_DEFAULT_MODEL=anthropic
AI_MAX_TOKENS=2000
AI_TIMEOUT=30
```

### 2. Performance Optimization
```php
// Add caching for similar prompts
$cacheKey = 'ai_' . md5($prompt);
$cachedResult = cache($cacheKey);

if ($cachedResult) {
    return $this->response->setJSON($cachedResult);
}

// Cache result for 1 hour
$result = $this->callAI($prompt);
cache($cacheKey, $result, 3600);
```

### 3. Monitoring and Logging
```php
// Log AI usage for monitoring
log_message('info', "AI Request: User {$userId}, Model: {$aiModel}, Tokens: {$tokenCount}");

// Track costs and usage
$this->logAIUsage($userId, $aiModel, $tokenCount, $processingTime);
```

## Testing

### 1. Unit Tests
```php
class AIControllerTest extends CIUnitTestCase
{
    public function testAIAnalysis()
    {
        $request = $this->createMock(RequestInterface::class);
        $request->method('isAJAX')->willReturn(true);
        $request->method('getPost')->willReturn('Test prompt');

        $controller = new AIController();
        $response = $controller->aiAnalysis();

        $this->assertInstanceOf(ResponseInterface::class, $response);
    }
}
```

### 2. Integration Tests
```javascript
// Frontend testing
describe('AI Analysis', function() {
    it('should send prompt to backend', function() {
        // Mock AJAX call
        spyOn($, 'ajax').and.callFake(function(options) {
            options.success({
                status: 'success',
                message: 'Test response'
            });
        });

        $('#analyzeButton').click();
        expect($.ajax).toHaveBeenCalled();
    });
});
```

## Customization Tips

### 1. Adding New AI Providers
```php
private function useCustomAI($prompt)
{
    $apiKey = env('CUSTOM_AI_API_KEY');
    $apiUrl = 'https://api.custom-ai.com/v1/chat';

    // Implement API call similar to existing providers
    // Return standardized response format
}
```

### 2. Custom Prompt Templates
```php
private function buildPrompt($data, $template = 'default')
{
    $templates = [
        'default' => "Analyze: {data}",
        'detailed' => "Provide detailed analysis of: {data}",
        'summary' => "Summarize: {data}"
    ];

    return str_replace('{data}', $data, $templates[$template]);
}
```

### 3. Response Processing
```php
private function processAIResponse($response, $format = 'text')
{
    switch ($format) {
        case 'json':
            return json_decode($response, true);
        case 'structured':
            return $this->parseStructuredResponse($response);
        default:
            return $response;
    }
}
```

## Conclusion

This implementation provides a robust, scalable AI analysis system that can be adapted for various use cases. Key benefits include:

- **Multi-provider support**: Flexibility to use different AI services
- **Real-time chat interface**: Intuitive user experience
- **Automatic data collection**: Seamless integration with existing forms
- **Performance monitoring**: Track usage and response times
- **Security features**: Input validation and rate limiting
- **Error handling**: Graceful failure management

The system can be easily customized for different domains by modifying the prompt templates and data collection logic while maintaining the core architecture.

## API Keys Required

To implement this system, you'll need API keys from:

1. **Anthropic Claude**: https://console.anthropic.com/
2. **Google Gemini**: https://ai.google.dev/
3. **DeepSeek**: https://platform.deepseek.com/

Remember to keep your API keys secure and never commit them to version control.
